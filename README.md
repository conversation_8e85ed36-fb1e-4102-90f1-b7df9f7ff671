# Autodebruin WordPress Theme

Een op maat gemaakt WordPress-thema voor Autodebruin.nl - Bedrijfswagen specialist sinds 1960.

## Overzicht

Dit thema is speciaal ontwikkeld voor Autodebruin, een bedrijfswagen specialist met meer dan 60 jaar ervaring. Het thema is geoptimaliseerd voor het tonen van occasions, bedrijfswagens en het bieden van een uitstekende gebruikerservaring.

## Features

### 🚗 Occasion Management
- Custom Post Type voor occasions
- Uitgebreide filtering mogelijkheden
- Taxonomieën voor merk, brandstof en transmissie
- Gedetailleerde specificaties per voertuig
- Afbeeldingen gallerij
- Prijs en lease calculators

### 🎨 Design & UX
- Volledig responsief design
- Modern en professioneel uiterlijk
- Snelle laadtijden
- SEO geoptimaliseerd
- Toegankelijk voor alle gebruikers

### 📱 Functionaliteiten
- AJAX filtering van occasions
- WhatsApp integratie
- Contact formulieren
- Lease calculator
- Zoekfunctionaliteit met suggesties
- Social media integratie

### ⚙️ Technische Features
- WordPress Customizer integratie
- Widget areas
- Custom menu's
- Schema.org markup
- Performance optimalisatie
- Cross-browser compatibiliteit

## Installatie

1. Upload het thema naar `/wp-content/themes/autodebruin/`
2. Activeer het thema in WordPress Admin
3. Ga naar Appearance > Customize om instellingen aan te passen
4. Installeer aanbevolen plugins (Contact Form 7, etc.)

## Configuratie

### Customizer Instellingen

Het thema biedt uitgebreide customizer opties:

#### Bedrijfsinformatie
- Telefoonnummer
- E-mailadres  
- Adres
- WhatsApp nummer

#### Social Media
- Facebook URL
- Twitter URL
- YouTube URL
- LinkedIn URL

#### Homepage Instellingen
- Hero titel en ondertitel
- Hero achtergrondafbeelding
- Aantal uitgelichte occasions
- Toon/verberg secties

#### Kleuren
- Primaire kleur
- Secundaire kleur
- Automatische kleur variaties

#### Openingstijden
- Weekdagen
- Zaterdag
- Zondag

### Menu's

Het thema ondersteunt twee menu locaties:
- **Primary Menu**: Hoofdnavigatie
- **Footer Menu**: Footer navigatie

### Widget Areas

Beschikbare widget areas:
- **Sidebar**: Algemene zijbalk
- **Footer 1, 2, 3**: Footer widgets

## Custom Post Types

### Occasions

Het thema registreert een custom post type "Occasion" met de volgende velden:

#### Basis Informatie
- Titel
- Beschrijving
- Uitgelichte afbeelding
- Gallerij afbeeldingen

#### Specificaties
- Prijs
- Kilometers
- Bouwjaar
- Kenteken
- Laadvermogen
- Vermogen (PK)
- Topsnelheid
- Gewicht
- Kleur
- Aantal deuren
- Energielabel

#### Taxonomieën
- **Merk**: Mercedes-Benz, Peugeot, Ford, etc.
- **Brandstof**: Diesel, Elektrisch, Benzine, etc.
- **Transmissie**: Handmatig, Automaat

## Templates

### Pagina Templates
- `front-page.php` - Homepage
- `page-contact.php` - Contact pagina
- `page-service.php` - Service pagina
- `page-financial-lease.php` - Financial lease pagina

### Archive Templates
- `archive-occasion.php` - Occasions overzicht
- `single-occasion.php` - Occasion detailpagina
- `taxonomy-merk.php` - Merk archief
- `taxonomy-brandstof.php` - Brandstof archief
- `taxonomy-transmissie.php` - Transmissie archief

### Overige Templates
- `search.php` - Zoekresultaten
- `404.php` - Foutpagina

## JavaScript Functionaliteiten

### AJAX Filtering
Occasions kunnen gefilterd worden zonder pagina refresh:
```javascript
// Filter occasions
filterOccasions(page);
```

### Lease Calculator
Interactieve lease calculator:
```javascript
// Calculate monthly payment
calculateLease();
```

### Contact Forms
AJAX contact formulier afhandeling:
```javascript
// Submit contact form
submitContactForm();
```

## CSS Structuur

Het thema gebruikt een modulaire CSS structuur:

```css
/* Base styles */
- Reset & typography
- Layout & grid system
- Components

/* Sections */
- Header & navigation
- Hero section
- USP section
- Occasions grid
- Footer

/* Components */
- Buttons
- Forms
- Cards
- Modals

/* Utilities */
- Helper classes
- Responsive utilities
```

## Performance Optimalisatie

### Afbeeldingen
- Lazy loading
- Responsive images
- WebP ondersteuning (via plugins)

### CSS & JavaScript
- Minificatie in productie
- Critical CSS inline
- Async loading van non-critical resources

### Caching
- Browser caching headers
- Compatibel met caching plugins

## SEO Optimalisatie

### Schema.org Markup
- Organization schema
- LocalBusiness schema
- Product schema voor occasions
- BreadcrumbList schema

### Meta Tags
- Open Graph tags
- Twitter Cards
- Canonical URLs
- Meta descriptions

## Browser Ondersteuning

- Chrome (laatste 2 versies)
- Firefox (laatste 2 versies)
- Safari (laatste 2 versies)
- Edge (laatste 2 versies)
- Internet Explorer 11+

## Aanbevolen Plugins

### Essentieel
- **Contact Form 7**: Contact formulieren
- **Yoast SEO**: SEO optimalisatie
- **W3 Total Cache**: Performance
- **Wordfence**: Beveiliging

### Optioneel
- **Advanced Custom Fields**: Extra velden
- **WP Rocket**: Premium caching
- **Smush**: Afbeelding optimalisatie

## Ontwikkeling

### Lokale Development
```bash
# Clone repository
git clone [repository-url]

# Install dependencies (if using build tools)
npm install

# Start development
npm run dev
```

### Build Process
```bash
# Production build
npm run build

# Watch for changes
npm run watch
```

## Support & Documentatie

Voor support en aanvullende documentatie:
- **Developer**: Pascal Bouman - Funnel Adviseur
- **Website**: https://funneladviseur.nl
- **Email**: <EMAIL>

## Changelog

### Version 1.0.0
- Initiële release
- Occasion management systeem
- Responsive design
- AJAX filtering
- Lease calculator
- Contact formulieren
- SEO optimalisatie

## Licentie

Dit thema is ontwikkeld voor Autodebruin.nl en is eigendom van Autodebruin. Alle rechten voorbehouden.

---

**Ontwikkeld door Pascal Bouman - Funnel Adviseur**  
*Specialist in WordPress ontwikkeling en digitale marketing*
