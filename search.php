<?php
/**
 * The template for displaying search results pages
 *
 * @package Autodebruin
 */

get_header();
?>

<main id="primary" class="site-main search-results">
    <div class="container">
        
        <header class="page-header">
            <h1 class="page-title">
                <?php
                printf(
                    esc_html__('Zoekresultaten voor: %s', 'autodebruin'),
                    '<span class="search-term">' . get_search_query() . '</span>'
                );
                ?>
            </h1>
            
            <div class="search-info">
                <?php
                global $wp_query;
                $total_results = $wp_query->found_posts;
                printf(
                    esc_html(_n('%d resultaat gevonden', '%d resultaten gevonden', $total_results, 'autodebruin')),
                    $total_results
                );
                ?>
            </div>
        </header>

        <div class="search-content">
            <div class="row">
                
                <!-- Search Results -->
                <div class="col-8">
                    
                    <!-- New Search Form -->
                    <div class="search-form-widget">
                        <h3><?php esc_html_e('Verfijn uw zoekopdracht', 'autodebruin'); ?></h3>
                        <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                            <div class="search-input-group">
                                <input type="search" 
                                       class="search-field" 
                                       placeholder="<?php echo esc_attr_x('Zoeken...', 'placeholder', 'autodebruin'); ?>" 
                                       value="<?php echo get_search_query(); ?>" 
                                       name="s" />
                                <button type="submit" class="search-submit">
                                    <i class="fas fa-search"></i>
                                    <span class="sr-only"><?php echo _x('Zoeken', 'submit button', 'autodebruin'); ?></span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Results -->
                    <div class="search-results-list">
                        <?php if (have_posts()) : ?>
                            
                            <?php while (have_posts()) : the_post(); ?>
                                
                                <article id="post-<?php the_ID(); ?>" <?php post_class('search-result-item'); ?>>
                                    
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="result-thumbnail">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('thumbnail'); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="result-content">
                                        <header class="result-header">
                                            <h2 class="result-title">
                                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h2>
                                            
                                            <div class="result-meta">
                                                <span class="post-type">
                                                    <?php
                                                    $post_type = get_post_type();
                                                    if ($post_type === 'occasion') {
                                                        esc_html_e('Occasion', 'autodebruin');
                                                    } elseif ($post_type === 'page') {
                                                        esc_html_e('Pagina', 'autodebruin');
                                                    } else {
                                                        esc_html_e('Bericht', 'autodebruin');
                                                    }
                                                    ?>
                                                </span>
                                                
                                                <?php if (get_post_type() === 'occasion') : ?>
                                                    <?php
                                                    $price = get_post_meta(get_the_ID(), '_occasion_price', true);
                                                    $year = get_post_meta(get_the_ID(), '_occasion_year', true);
                                                    $kilometers = get_post_meta(get_the_ID(), '_occasion_kilometers', true);
                                                    ?>
                                                    
                                                    <?php if ($price) : ?>
                                                        <span class="occasion-price">€ <?php echo number_format($price, 0, ',', '.'); ?>,-</span>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($year) : ?>
                                                        <span class="occasion-year"><?php echo esc_html($year); ?></span>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($kilometers) : ?>
                                                        <span class="occasion-km"><?php echo number_format($kilometers, 0, ',', '.'); ?> km</span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </header>
                                        
                                        <div class="result-excerpt">
                                            <?php the_excerpt(); ?>
                                        </div>
                                        
                                        <div class="result-footer">
                                            <a href="<?php the_permalink(); ?>" class="read-more">
                                                <?php
                                                if (get_post_type() === 'occasion') {
                                                    esc_html_e('Bekijk occasion', 'autodebruin');
                                                } else {
                                                    esc_html_e('Lees meer', 'autodebruin');
                                                }
                                                ?>
                                                <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                    
                                </article>
                                
                            <?php endwhile; ?>
                            
                            <!-- Pagination -->
                            <?php
                            the_posts_navigation(array(
                                'prev_text' => __('&larr; Vorige resultaten', 'autodebruin'),
                                'next_text' => __('Volgende resultaten &rarr;', 'autodebruin'),
                            ));
                            ?>
                            
                        <?php else : ?>
                            
                            <div class="no-results">
                                <h2><?php esc_html_e('Geen resultaten gevonden', 'autodebruin'); ?></h2>
                                <p><?php esc_html_e('Sorry, maar er zijn geen resultaten gevonden voor uw zoekopdracht. Probeer het met andere zoektermen.', 'autodebruin'); ?></p>
                                
                                <div class="search-suggestions">
                                    <h3><?php esc_html_e('Zoektips:', 'autodebruin'); ?></h3>
                                    <ul>
                                        <li><?php esc_html_e('Controleer of alle woorden correct gespeld zijn', 'autodebruin'); ?></li>
                                        <li><?php esc_html_e('Probeer andere zoektermen', 'autodebruin'); ?></li>
                                        <li><?php esc_html_e('Probeer meer algemene zoektermen', 'autodebruin'); ?></li>
                                        <li><?php esc_html_e('Probeer minder zoektermen', 'autodebruin'); ?></li>
                                    </ul>
                                </div>
                            </div>
                            
                        <?php endif; ?>
                    </div>
                    
                </div>

                <!-- Sidebar -->
                <div class="col-4">
                    <div class="search-sidebar">
                        
                        <!-- Popular Searches -->
                        <div class="widget popular-searches">
                            <h3><?php esc_html_e('Populaire zoekopdrachten', 'autodebruin'); ?></h3>
                            <ul class="popular-searches-list">
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'Mercedes', home_url('/'))); ?>">Mercedes</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'Peugeot', home_url('/'))); ?>">Peugeot</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'Ford Transit', home_url('/'))); ?>">Ford Transit</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'Volkswagen Caddy', home_url('/'))); ?>">Volkswagen Caddy</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'Renault Master', home_url('/'))); ?>">Renault Master</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'elektrisch', home_url('/'))); ?>">Elektrisch</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'diesel', home_url('/'))); ?>">Diesel</a></li>
                                <li><a href="<?php echo esc_url(add_query_arg('s', 'automaat', home_url('/'))); ?>">Automaat</a></li>
                            </ul>
                        </div>

                        <!-- Search by Category -->
                        <div class="widget search-categories">
                            <h3><?php esc_html_e('Zoek per categorie', 'autodebruin'); ?></h3>
                            <ul class="category-links">
                                <li>
                                    <a href="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>">
                                        <i class="fas fa-car"></i>
                                        <?php esc_html_e('Alle occasions', 'autodebruin'); ?>
                                    </a>
                                </li>
                                
                                <?php
                                $merken = get_terms(array(
                                    'taxonomy' => 'merk',
                                    'hide_empty' => true,
                                    'number' => 5,
                                ));
                                
                                foreach ($merken as $merk) :
                                    ?>
                                    <li>
                                        <a href="<?php echo esc_url(get_term_link($merk)); ?>">
                                            <i class="fas fa-tag"></i>
                                            <?php echo esc_html($merk->name); ?>
                                            <span class="count">(<?php echo $merk->count; ?>)</span>
                                        </a>
                                    </li>
                                    <?php
                                endforeach;
                                ?>
                            </ul>
                        </div>

                        <!-- Quick Search Form -->
                        <div class="widget quick-search">
                            <h3><?php esc_html_e('Zoek een occasion', 'autodebruin'); ?></h3>
                            
                            <form class="occasion-search-form" method="get" action="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>">
                                <div class="search-field">
                                    <label for="search-merk"><?php esc_html_e('Merk', 'autodebruin'); ?></label>
                                    <select name="merk" id="search-merk">
                                        <option value=""><?php esc_html_e('Alle merken', 'autodebruin'); ?></option>
                                        <?php
                                        $merken = get_terms(array(
                                            'taxonomy' => 'merk',
                                            'hide_empty' => true,
                                        ));
                                        foreach ($merken as $merk) {
                                            echo '<option value="' . esc_attr($merk->slug) . '">' . esc_html($merk->name) . '</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                                
                                <div class="search-field">
                                    <label for="search-price"><?php esc_html_e('Max. Prijs', 'autodebruin'); ?></label>
                                    <select name="max_price" id="search-price">
                                        <option value=""><?php esc_html_e('Geen limiet', 'autodebruin'); ?></option>
                                        <option value="10000">€ 10.000</option>
                                        <option value="15000">€ 15.000</option>
                                        <option value="20000">€ 20.000</option>
                                        <option value="25000">€ 25.000</option>
                                        <option value="30000">€ 30.000</option>
                                        <option value="50000">€ 50.000</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-block">
                                    <?php esc_html_e('Zoek occasions', 'autodebruin'); ?>
                                </button>
                            </form>
                        </div>

                        <!-- Contact Widget -->
                        <div class="widget contact-widget">
                            <h3><?php esc_html_e('Hulp bij zoeken?', 'autodebruin'); ?></h3>
                            <p><?php esc_html_e('Kunt u niet vinden wat u zoekt? Onze specialisten helpen u graag!', 'autodebruin'); ?></p>
                            
                            <div class="contact-methods">
                                <a href="tel:0180312484" class="contact-method">
                                    <i class="fas fa-phone"></i>
                                    <span>0180-312484</span>
                                </a>
                                
                                <a href="mailto:<EMAIL>" class="contact-method">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </a>
                                
                                <a href="https://wa.me/31180312484" class="contact-method" target="_blank" rel="noopener">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp</span>
                                </a>
                            </div>
                        </div>

                    </div>
                </div>
                
            </div>
        </div>

    </div>
</main>

<?php
get_footer();
?>
