<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package Autodebruin
 */

get_header();
?>

<main id="primary" class="site-main error-404-page">
    <div class="container">
        
        <section class="error-404 not-found">
            <header class="page-header">
                <h1 class="page-title"><?php esc_html_e('Oops! Pagina niet gevonden', 'autodebruin'); ?></h1>
            </header>

            <div class="page-content">
                <div class="row">
                    <div class="col-8">
                        <div class="error-content">
                            <div class="error-number">404</div>
                            
                            <h2><?php esc_html_e('De pagina die u zoekt bestaat niet', 'autodebruin'); ?></h2>
                            
                            <p><?php esc_html_e('Het lijkt erop dat er niets gevonden is op deze locatie. Misschien kunt u een van de onderstaande links proberen of een zoekopdracht uitvoeren?', 'autodebruin'); ?></p>
                            
                            <!-- Search Form -->
                            <div class="error-search">
                                <h3><?php esc_html_e('Zoeken op de website', 'autodebruin'); ?></h3>
                                <?php get_search_form(); ?>
                            </div>
                            
                            <!-- Quick Links -->
                            <div class="quick-links">
                                <h3><?php esc_html_e('Populaire pagina\'s', 'autodebruin'); ?></h3>
                                <ul>
                                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Homepage', 'autodebruin'); ?></a></li>
                                    <li><a href="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>"><?php esc_html_e('Occasions', 'autodebruin'); ?></a></li>
                                    <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('service'))); ?>"><?php esc_html_e('Service', 'autodebruin'); ?></a></li>
                                    <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('financial-lease'))); ?>"><?php esc_html_e('Financial Lease', 'autodebruin'); ?></a></li>
                                    <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>"><?php esc_html_e('Contact', 'autodebruin'); ?></a></li>
                                </ul>
                            </div>
                            
                            <!-- Recent Occasions -->
                            <div class="recent-occasions">
                                <h3><?php esc_html_e('Nieuwste occasions', 'autodebruin'); ?></h3>
                                <?php
                                $recent_occasions = new WP_Query(array(
                                    'post_type' => 'occasion',
                                    'posts_per_page' => 3,
                                    'post_status' => 'publish'
                                ));
                                
                                if ($recent_occasions->have_posts()) :
                                    echo '<ul class="recent-occasions-list">';
                                    while ($recent_occasions->have_posts()) : $recent_occasions->the_post();
                                        $price = get_post_meta(get_the_ID(), '_occasion_price', true);
                                        echo '<li>';
                                        echo '<a href="' . get_permalink() . '">';
                                        echo '<strong>' . get_the_title() . '</strong>';
                                        if ($price) {
                                            echo '<span class="price">€ ' . number_format($price, 0, ',', '.') . ',-</span>';
                                        }
                                        echo '</a>';
                                        echo '</li>';
                                    endwhile;
                                    echo '</ul>';
                                    wp_reset_postdata();
                                endif;
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-4">
                        <div class="error-sidebar">
                            
                            <!-- Contact Widget -->
                            <div class="contact-widget">
                                <h3><?php esc_html_e('Hulp nodig?', 'autodebruin'); ?></h3>
                                <p><?php esc_html_e('Kunt u niet vinden wat u zoekt? Neem gerust contact met ons op. Wij helpen u graag verder!', 'autodebruin'); ?></p>
                                
                                <div class="contact-methods">
                                    <div class="contact-method">
                                        <i class="fas fa-phone"></i>
                                        <div>
                                            <strong><?php esc_html_e('Bel ons', 'autodebruin'); ?></strong>
                                            <a href="tel:0180312484">0180-312484</a>
                                        </div>
                                    </div>
                                    
                                    <div class="contact-method">
                                        <i class="fas fa-envelope"></i>
                                        <div>
                                            <strong><?php esc_html_e('E-mail', 'autodebruin'); ?></strong>
                                            <a href="mailto:<EMAIL>"><EMAIL></a>
                                        </div>
                                    </div>
                                    
                                    <div class="contact-method">
                                        <i class="fab fa-whatsapp"></i>
                                        <div>
                                            <strong><?php esc_html_e('WhatsApp', 'autodebruin'); ?></strong>
                                            <a href="https://wa.me/31180312484" target="_blank" rel="noopener">Stuur bericht</a>
                                        </div>
                                    </div>
                                </div>
                                
                                <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="btn btn-primary btn-block">
                                    <?php esc_html_e('Contactpagina', 'autodebruin'); ?>
                                </a>
                            </div>
                            
                            <!-- Search Widget -->
                            <div class="search-widget">
                                <h3><?php esc_html_e('Zoek een occasion', 'autodebruin'); ?></h3>
                                
                                <form class="occasion-search-form" method="get" action="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>">
                                    <div class="search-field">
                                        <label for="search-merk"><?php esc_html_e('Merk', 'autodebruin'); ?></label>
                                        <select name="merk" id="search-merk">
                                            <option value=""><?php esc_html_e('Alle merken', 'autodebruin'); ?></option>
                                            <?php
                                            $merken = get_terms(array(
                                                'taxonomy' => 'merk',
                                                'hide_empty' => true,
                                            ));
                                            foreach ($merken as $merk) {
                                                echo '<option value="' . esc_attr($merk->slug) . '">' . esc_html($merk->name) . '</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="search-field">
                                        <label for="search-brandstof"><?php esc_html_e('Brandstof', 'autodebruin'); ?></label>
                                        <select name="brandstof" id="search-brandstof">
                                            <option value=""><?php esc_html_e('Alle brandstoffen', 'autodebruin'); ?></option>
                                            <?php
                                            $brandstoffen = get_terms(array(
                                                'taxonomy' => 'brandstof',
                                                'hide_empty' => true,
                                            ));
                                            foreach ($brandstoffen as $brandstof) {
                                                echo '<option value="' . esc_attr($brandstof->slug) . '">' . esc_html($brandstof->name) . '</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="search-field">
                                        <label for="search-price"><?php esc_html_e('Max. Prijs', 'autodebruin'); ?></label>
                                        <select name="max_price" id="search-price">
                                            <option value=""><?php esc_html_e('Geen limiet', 'autodebruin'); ?></option>
                                            <option value="10000">€ 10.000</option>
                                            <option value="15000">€ 15.000</option>
                                            <option value="20000">€ 20.000</option>
                                            <option value="25000">€ 25.000</option>
                                            <option value="30000">€ 30.000</option>
                                            <option value="50000">€ 50.000</option>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <?php esc_html_e('Zoek occasions', 'autodebruin'); ?>
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Company Info -->
                            <div class="company-info-widget">
                                <h3><?php esc_html_e('Over Autodebruin', 'autodebruin'); ?></h3>
                                <p><?php esc_html_e('Sinds 1960 uw betrouwbare partner voor bedrijfswagens. Meer dan 60 jaar ervaring in de automotive sector.', 'autodebruin'); ?></p>
                                
                                <div class="company-stats">
                                    <div class="stat">
                                        <strong>60+</strong>
                                        <span><?php esc_html_e('Jaar ervaring', 'autodebruin'); ?></span>
                                    </div>
                                    <div class="stat">
                                        <strong>1000+</strong>
                                        <span><?php esc_html_e('Tevreden klanten', 'autodebruin'); ?></span>
                                    </div>
                                    <div class="stat">
                                        <strong>100+</strong>
                                        <span><?php esc_html_e('Occasions op voorraad', 'autodebruin'); ?></span>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </div>
</main>

<?php
get_footer();
?>
