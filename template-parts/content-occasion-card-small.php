<?php
/**
 * Template part for displaying small occasion cards (for similar occasions)
 *
 * @package Autodebruin
 */

$price = get_post_meta(get_the_ID(), '_occasion_price', true);
$year = get_post_meta(get_the_ID(), '_occasion_year', true);
$kilometers = get_post_meta(get_the_ID(), '_occasion_kilometers', true);
$merk_terms = get_the_terms(get_the_ID(), 'merk');
$merk = $merk_terms && !is_wp_error($merk_terms) ? $merk_terms[0]->name : '';
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('occasion-card-small'); ?>>
    
    <div class="occasion-image-small">
        <a href="<?php the_permalink(); ?>">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('thumbnail', array('alt' => get_the_title())); ?>
            <?php else : ?>
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/placeholder-car-small.jpg" 
                     alt="<?php the_title_attribute(); ?>" 
                     class="placeholder-image">
            <?php endif; ?>
        </a>
    </div>

    <div class="occasion-content-small">
        <h4 class="occasion-title-small">
            <a href="<?php the_permalink(); ?>"><?php echo esc_html($merk); ?> <?php the_title(); ?></a>
        </h4>
        
        <div class="occasion-specs-small">
            <?php if ($year) : ?>
                <span class="spec"><?php echo esc_html($year); ?></span>
            <?php endif; ?>
            
            <?php if ($kilometers) : ?>
                <span class="spec"><?php echo number_format($kilometers, 0, ',', '.'); ?> km</span>
            <?php endif; ?>
        </div>
        
        <?php if ($price) : ?>
            <div class="occasion-price-small">
                <strong>€ <?php echo number_format($price, 0, ',', '.'); ?>,-</strong>
            </div>
        <?php endif; ?>
    </div>

</article>
