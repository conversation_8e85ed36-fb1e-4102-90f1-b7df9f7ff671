<?php
/**
 * Template part for displaying a message that posts cannot be found
 *
 * @package Autodebruin
 */
?>

<section class="no-results not-found">
    <header class="page-header">
        <h1 class="page-title"><?php esc_html_e('Niets gevonden', 'autodebruin'); ?></h1>
    </header>

    <div class="page-content">
        <?php
        if (is_home() && current_user_can('publish_posts')) :

            printf(
                '<p>' . wp_kses(
                    __('Klaar om je eerste bericht te publiceren? <a href="%1$s">Begin hier</a>.', 'autodebruin'),
                    array(
                        'a' => array(
                            'href' => array(),
                        ),
                    )
                ) . '</p>',
                esc_url(admin_url('post-new.php'))
            );

        elseif (is_search()) :
            ?>

            <p><?php esc_html_e('Sorry, maar er zijn geen resultaten gevonden voor uw zoekopdracht. <PERSON><PERSON><PERSON> het met andere zoektermen.', 'autodebruin'); ?></p>
            <?php
            get_search_form();

        else :
            ?>

            <p><?php esc_html_e('Het lijkt erop dat we niet kunnen vinden wat u zoekt. Misschien kan zoeken helpen.', 'autodebruin'); ?></p>
            <?php
            get_search_form();

        endif;
        ?>
    </div>
</section>
