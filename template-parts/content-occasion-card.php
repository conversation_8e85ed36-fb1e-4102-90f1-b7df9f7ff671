<?php
/**
 * Template part for displaying occasion cards
 *
 * @package Autodebruin
 */

$price = get_post_meta(get_the_ID(), '_occasion_price', true);
$kilometers = get_post_meta(get_the_ID(), '_occasion_kilometers', true);
$year = get_post_meta(get_the_ID(), '_occasion_year', true);
$brandstof_terms = get_the_terms(get_the_ID(), 'brandstof');
$transmissie_terms = get_the_terms(get_the_ID(), 'transmissie');
$merk_terms = get_the_terms(get_the_ID(), 'merk');
$rijklaar_kosten = get_post_meta(get_the_ID(), '_occasion_rijklaar_kosten', true);

$brandstof = $brandstof_terms && !is_wp_error($brandstof_terms) ? $brandstof_terms[0]->name : '';
$transmissie = $transmissie_terms && !is_wp_error($transmissie_terms) ? $transmissie_terms[0]->name : '';
$merk = $merk_terms && !is_wp_error($merk_terms) ? $merk_terms[0]->name : '';
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('occasion-card'); ?>>
    
    <div class="occasion-image">
        <a href="<?php the_permalink(); ?>">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('occasion-thumbnail', array('alt' => get_the_title())); ?>
            <?php else : ?>
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/placeholder-car.jpg" 
                     alt="<?php the_title_attribute(); ?>" 
                     class="placeholder-image">
            <?php endif; ?>
        </a>
        
        <?php if (get_post_meta(get_the_ID(), '_featured', true) == '1') : ?>
            <div class="featured-badge">
                <span><?php esc_html_e('Uitgelicht', 'autodebruin'); ?></span>
            </div>
        <?php endif; ?>
    </div>

    <div class="occasion-content">
        
        <div class="occasion-header">
            <h3 class="occasion-brand">
                <a href="<?php the_permalink(); ?>"><?php echo esc_html($merk); ?></a>
            </h3>
            <h4 class="occasion-model">
                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
            </h4>
            <p class="occasion-subtitle">
                <a href="<?php the_permalink(); ?>"><?php echo esc_html(get_the_excerpt()); ?></a>
            </p>
        </div>

        <div class="occasion-price">
            <?php if ($price) : ?>
                <div class="main-price">
                    <?php esc_html_e('Nu voor:', 'autodebruin'); ?>
                    <strong>€ <?php echo number_format($price, 0, ',', '.'); ?>,-</strong>
                </div>
            <?php endif; ?>
            
            <?php if ($rijklaar_kosten) : ?>
                <div class="rijklaar-price">
                    <?php esc_html_e('Rijklaar voor:', 'autodebruin'); ?>
                    <ul>
                        <li>€ <?php echo esc_html($rijklaar_kosten); ?>,-</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>

        <div class="occasion-specs">
            <?php if ($year) : ?>
                <div class="spec-item">
                    <a href="<?php the_permalink(); ?>">
                        <i class="fas fa-calendar"></i>
                        <span><?php esc_html_e('Bouwjaar:', 'autodebruin'); ?> <?php echo esc_html($year); ?></span>
                    </a>
                </div>
            <?php endif; ?>
            
            <?php if ($kilometers) : ?>
                <div class="spec-item">
                    <a href="<?php the_permalink(); ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span><?php esc_html_e('Kilometers:', 'autodebruin'); ?> <?php echo number_format($kilometers, 0, ',', '.'); ?> km</span>
                    </a>
                </div>
            <?php endif; ?>
            
            <?php if ($brandstof) : ?>
                <div class="spec-item">
                    <a href="<?php the_permalink(); ?>">
                        <i class="fas fa-gas-pump"></i>
                        <span><?php esc_html_e('Brandstof:', 'autodebruin'); ?> <?php echo esc_html($brandstof); ?></span>
                    </a>
                </div>
            <?php endif; ?>
            
            <?php if ($transmissie) : ?>
                <div class="spec-item">
                    <a href="<?php the_permalink(); ?>">
                        <i class="fas fa-cogs"></i>
                        <span><?php esc_html_e('Transmissie:', 'autodebruin'); ?> <?php echo esc_html($transmissie); ?></span>
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <div class="occasion-contact">
            <div class="contact-info">
                <p><?php esc_html_e('Neem contact op', 'autodebruin'); ?></p>
                <ul>
                    <li><a href="tel:0180312484">0180 - 312484</a></li>
                    <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                </ul>
            </div>
        </div>

        <div class="occasion-actions">
            <a href="<?php the_permalink(); ?>" class="btn btn-primary btn-block">
                <?php esc_html_e('Bekijk auto', 'autodebruin'); ?>
            </a>
        </div>

    </div>

</article>
