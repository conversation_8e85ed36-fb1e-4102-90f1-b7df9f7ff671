<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 *
 * @package Autodebruin
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php if (have_posts()) : ?>

            <header class="page-header">
                <?php
                if (is_home() && !is_front_page()) :
                    ?>
                    <h1 class="page-title"><?php single_post_title(); ?></h1>
                    <?php
                elseif (is_search()) :
                    ?>
                    <h1 class="page-title">
                        <?php
                        printf(
                            esc_html__('Zoekresultaten voor: %s', 'autodebruin'),
                            '<span>' . get_search_query() . '</span>'
                        );
                        ?>
                    </h1>
                    <?php
                elseif (is_archive()) :
                    the_archive_title('<h1 class="page-title">', '</h1>');
                    the_archive_description('<div class="archive-description">', '</div>');
                endif;
                ?>
            </header>

            <div class="posts-container">
                <?php
                // Start the Loop
                while (have_posts()) :
                    the_post();
                    
                    /*
                     * Include the Post-Type-specific template for the content.
                     * If you want to override this in a child theme, then include a file
                     * called content-___.php (where ___ is the Post Type name) and that will be used instead.
                     */
                    get_template_part('template-parts/content', get_post_type());

                endwhile;
                ?>
            </div>

            <?php
            // Previous/next page navigation
            the_posts_navigation(array(
                'prev_text' => __('&larr; Vorige berichten', 'autodebruin'),
                'next_text' => __('Volgende berichten &rarr;', 'autodebruin'),
            ));

        else :

            get_template_part('template-parts/content', 'none');

        endif;
        ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();
?>
