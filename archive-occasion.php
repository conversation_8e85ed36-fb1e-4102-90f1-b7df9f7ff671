<?php
/**
 * The template for displaying occasion archive pages
 *
 * @package Autodebruin
 */

get_header();
?>

<main id="primary" class="site-main occasions-archive">
    <div class="container">
        
        <!-- Page Header -->
        <header class="page-header">
            <h1 class="page-title"><?php esc_html_e('Onze occasions', 'autodebruin'); ?></h1>
            <div class="page-description">
                <p><strong><?php esc_html_e('Waar wij voor staan en wat u kunt verwachten.', 'autodebruin'); ?></strong></p>
                <p><?php esc_html_e('Autodebruin is in 1959 opgericht door C. de Bruin. Opgegroeid in de paardenhandel en zelf werkzaam in de zuivelhandel heeft hij zijn activiteiten sinds 1960 volledig gericht op het handelen in gebruikte personenauto\'s en Bedrijfswagens. Al ruim 60 jaar lang leveren wij uitstekende bedrijfswagens en personenauto\'s en met professionele service op maat. Ook buiten de landsgrenzen van Nederland staat de Autodebruin bekend als een professioneel bedrijf in de automotive. Dit omdat wij onze auto\'s ook naar het buitenland exporteren.', 'autodebruin'); ?></p>
            </div>
        </header>

        <!-- Filter Section -->
        <section class="occasions-filters">
            <div class="filter-toggle">
                <button id="filter-toggle-btn" class="btn btn-secondary">
                    <i class="fas fa-filter"></i>
                    <?php esc_html_e('Wijzig filters', 'autodebruin'); ?>
                </button>
            </div>
            
            <div id="filter-panel" class="filter-panel">
                <h3><?php esc_html_e('Zoekopdracht verfijnen', 'autodebruin'); ?></h3>
                
                <form id="occasions-filter-form" class="occasions-filter-form">
                    <div class="filter-grid">
                        
                        <!-- Soort Filter -->
                        <div class="filter-group">
                            <label for="filter-soort"><?php esc_html_e('Soort', 'autodebruin'); ?></label>
                            <select name="soort" id="filter-soort">
                                <option value=""><?php esc_html_e('Alle soorten', 'autodebruin'); ?></option>
                                <option value="bedrijfswagen"><?php esc_html_e('Bedrijfswagen', 'autodebruin'); ?></option>
                                <option value="personenauto"><?php esc_html_e('Personenauto', 'autodebruin'); ?></option>
                            </select>
                        </div>

                        <!-- Merk Filter -->
                        <div class="filter-group">
                            <label for="filter-merk"><?php esc_html_e('Merk', 'autodebruin'); ?></label>
                            <select name="merk" id="filter-merk">
                                <option value=""><?php esc_html_e('Alle merken', 'autodebruin'); ?></option>
                                <?php
                                $merken = get_terms(array(
                                    'taxonomy' => 'merk',
                                    'hide_empty' => true,
                                ));
                                foreach ($merken as $merk) {
                                    $selected = (isset($_GET['merk']) && $_GET['merk'] == $merk->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($merk->slug) . '" ' . $selected . '>' . esc_html($merk->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Model Filter -->
                        <div class="filter-group">
                            <label for="filter-model"><?php esc_html_e('Model', 'autodebruin'); ?></label>
                            <input type="text" name="model" id="filter-model" placeholder="<?php esc_attr_e('Zoek model...', 'autodebruin'); ?>">
                        </div>

                        <!-- Prijs Filter -->
                        <div class="filter-group">
                            <label for="filter-price"><?php esc_html_e('Prijs', 'autodebruin'); ?></label>
                            <div class="price-range">
                                <select name="min_price" id="filter-min-price">
                                    <option value=""><?php esc_html_e('Min. prijs', 'autodebruin'); ?></option>
                                    <option value="5000">€ 5.000</option>
                                    <option value="10000">€ 10.000</option>
                                    <option value="15000">€ 15.000</option>
                                    <option value="20000">€ 20.000</option>
                                    <option value="25000">€ 25.000</option>
                                    <option value="30000">€ 30.000</option>
                                </select>
                                <select name="max_price" id="filter-max-price">
                                    <option value=""><?php esc_html_e('Max. prijs', 'autodebruin'); ?></option>
                                    <option value="10000">€ 10.000</option>
                                    <option value="15000">€ 15.000</option>
                                    <option value="20000">€ 20.000</option>
                                    <option value="25000">€ 25.000</option>
                                    <option value="30000">€ 30.000</option>
                                    <option value="50000">€ 50.000</option>
                                </select>
                            </div>
                        </div>

                        <!-- Kleur Filter -->
                        <div class="filter-group">
                            <label for="filter-kleur"><?php esc_html_e('Kleur', 'autodebruin'); ?></label>
                            <select name="kleur" id="filter-kleur">
                                <option value=""><?php esc_html_e('Alle kleuren', 'autodebruin'); ?></option>
                                <option value="wit"><?php esc_html_e('Wit', 'autodebruin'); ?></option>
                                <option value="zwart"><?php esc_html_e('Zwart', 'autodebruin'); ?></option>
                                <option value="grijs"><?php esc_html_e('Grijs', 'autodebruin'); ?></option>
                                <option value="blauw"><?php esc_html_e('Blauw', 'autodebruin'); ?></option>
                                <option value="rood"><?php esc_html_e('Rood', 'autodebruin'); ?></option>
                            </select>
                        </div>

                        <!-- Brandstof Filter -->
                        <div class="filter-group">
                            <label for="filter-brandstof"><?php esc_html_e('Brandstof', 'autodebruin'); ?></label>
                            <select name="brandstof" id="filter-brandstof">
                                <option value=""><?php esc_html_e('Alle brandstoffen', 'autodebruin'); ?></option>
                                <?php
                                $brandstoffen = get_terms(array(
                                    'taxonomy' => 'brandstof',
                                    'hide_empty' => true,
                                ));
                                foreach ($brandstoffen as $brandstof) {
                                    $selected = (isset($_GET['brandstof']) && $_GET['brandstof'] == $brandstof->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($brandstof->slug) . '" ' . $selected . '>' . esc_html($brandstof->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Transmissie Filter -->
                        <div class="filter-group">
                            <label for="filter-transmissie"><?php esc_html_e('Transmissie', 'autodebruin'); ?></label>
                            <select name="transmissie" id="filter-transmissie">
                                <option value=""><?php esc_html_e('Alle transmissies', 'autodebruin'); ?></option>
                                <?php
                                $transmissies = get_terms(array(
                                    'taxonomy' => 'transmissie',
                                    'hide_empty' => true,
                                ));
                                foreach ($transmissies as $transmissie) {
                                    $selected = (isset($_GET['transmissie']) && $_GET['transmissie'] == $transmissie->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($transmissie->slug) . '" ' . $selected . '>' . esc_html($transmissie->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Bouwjaar Filter -->
                        <div class="filter-group">
                            <label for="filter-year"><?php esc_html_e('Bouwjaar', 'autodebruin'); ?></label>
                            <div class="year-range">
                                <select name="min_year" id="filter-min-year">
                                    <option value=""><?php esc_html_e('Van jaar', 'autodebruin'); ?></option>
                                    <?php
                                    $current_year = date('Y');
                                    for ($year = $current_year; $year >= 2000; $year--) {
                                        echo '<option value="' . $year . '">' . $year . '</option>';
                                    }
                                    ?>
                                </select>
                                <select name="max_year" id="filter-max-year">
                                    <option value=""><?php esc_html_e('Tot jaar', 'autodebruin'); ?></option>
                                    <?php
                                    for ($year = $current_year; $year >= 2000; $year--) {
                                        echo '<option value="' . $year . '">' . $year . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <!-- Laadvermogen Filter -->
                        <div class="filter-group">
                            <label for="filter-laadvermogen"><?php esc_html_e('Laadvermogen', 'autodebruin'); ?></label>
                            <select name="min_laadvermogen" id="filter-laadvermogen">
                                <option value=""><?php esc_html_e('Min. laadvermogen', 'autodebruin'); ?></option>
                                <option value="500">500 kg</option>
                                <option value="750">750 kg</option>
                                <option value="1000">1000 kg</option>
                                <option value="1250">1250 kg</option>
                                <option value="1500">1500 kg</option>
                            </select>
                        </div>

                        <!-- BTW Filter -->
                        <div class="filter-group">
                            <label for="filter-btw"><?php esc_html_e('BTW', 'autodebruin'); ?></label>
                            <select name="btw" id="filter-btw">
                                <option value=""><?php esc_html_e('Alle prijzen', 'autodebruin'); ?></option>
                                <option value="incl"><?php esc_html_e('Incl. BTW', 'autodebruin'); ?></option>
                                <option value="excl"><?php esc_html_e('Excl. BTW', 'autodebruin'); ?></option>
                            </select>
                        </div>

                    </div>
                    
                    <div class="filter-actions">
                        <button type="submit" class="btn btn-primary">
                            <?php esc_html_e('Bekijk het resultaat', 'autodebruin'); ?>
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <?php esc_html_e('Reset filters', 'autodebruin'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </section>

        <!-- Results Section -->
        <section class="occasions-results">
            <div class="results-header">
                <div class="results-count">
                    <?php
                    global $wp_query;
                    $total_occasions = $wp_query->found_posts;
                    printf(
                        esc_html(_n('%d Occasion beschikbaar', '%d Occasions beschikbaar', $total_occasions, 'autodebruin')),
                        $total_occasions
                    );
                    ?>
                </div>
                
                <div class="results-sorting">
                    <label for="sort-occasions"><?php esc_html_e('Sorteer op:', 'autodebruin'); ?></label>
                    <select id="sort-occasions" name="orderby">
                        <option value="date"><?php esc_html_e('Nieuwste eerst', 'autodebruin'); ?></option>
                        <option value="price_low"><?php esc_html_e('Prijs laag naar hoog', 'autodebruin'); ?></option>
                        <option value="price_high"><?php esc_html_e('Prijs hoog naar laag', 'autodebruin'); ?></option>
                        <option value="year_new"><?php esc_html_e('Bouwjaar nieuw naar oud', 'autodebruin'); ?></option>
                        <option value="year_old"><?php esc_html_e('Bouwjaar oud naar nieuw', 'autodebruin'); ?></option>
                        <option value="km_low"><?php esc_html_e('Kilometers laag naar hoog', 'autodebruin'); ?></option>
                    </select>
                </div>
            </div>

            <div id="occasions-grid" class="occasions-grid">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <?php get_template_part('template-parts/content', 'occasion-card'); ?>
                    <?php endwhile; ?>
                <?php else : ?>
                    <div class="no-occasions">
                        <h3><?php esc_html_e('Geen occasions gevonden', 'autodebruin'); ?></h3>
                        <p><?php esc_html_e('Er zijn geen occasions die voldoen aan uw zoekcriteria. Probeer uw filters aan te passen.', 'autodebruin'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php
            $pagination_args = array(
                'prev_text' => '&lt;',
                'next_text' => '&gt;',
                'type' => 'array',
                'current' => max(1, get_query_var('paged')),
                'total' => $wp_query->max_num_pages,
            );
            
            $pagination_links = paginate_links($pagination_args);
            
            if ($pagination_links) :
                ?>
                <nav class="occasions-pagination">
                    <ul class="pagination">
                        <?php foreach ($pagination_links as $link) : ?>
                            <li><?php echo $link; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </nav>
                <?php
            endif;
            ?>
        </section>

    </div>
</main>

<?php
get_footer();
?>
