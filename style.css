/*
Theme Name: Autodebruin
Description: Op maat gemaakt WordPress-thema voor Autodebruin.nl - Bedrijfswagen specialist sinds 1960
Author: <PERSON> - Funnel Adviseur
Author URI: https://funneladviseur.nl
Version: 1.0.0
License: GPL v2 or later
Text Domain: autodebruin
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

a:hover {
    opacity: 0.8;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Layout & Grid System
   ========================================================================== */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333%; }
.col-2 { flex: 0 0 16.666%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* ==========================================================================
   Header Styles
   ========================================================================== */

.site-header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: #1a1a1a;
    color: #fff;
    padding: 10px 0;
    font-size: 0.9rem;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info a {
    color: #fff;
    margin-right: 20px;
}

.social-links a {
    color: #fff;
    margin-left: 10px;
    font-size: 1.2rem;
}

.header-main {
    padding: 15px 0;
}

.header-main .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-logo img {
    max-height: 60px;
}

/* ==========================================================================
   Navigation Styles
   ========================================================================== */

.main-navigation ul {
    list-style: none;
    display: flex;
    align-items: center;
}

.main-navigation li {
    margin-left: 30px;
}

.main-navigation a {
    font-weight: 500;
    color: #333;
    padding: 10px 0;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.main-navigation a:hover,
.main-navigation .current-menu-item a {
    color: #e74c3c;
    border-bottom-color: #e74c3c;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* ==========================================================================
   Hero Section - Autodebruin Huisstijl
   ========================================================================== */

.hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ffffff;
    padding: 100px 0 120px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-inner {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-content-container {
    max-width: 600px;
    text-align: left;
    animation: fadeInUp 1s ease-out;
}

.hero-heading-main {
    margin-bottom: 20px;
    animation: fadeInLeft 1s ease-out 0.1s both;
}

.hero-heading-main h2 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-heading-sub {
    margin-bottom: 40px;
    animation: fadeInLeft 1s ease-out 0.2s both;
}

.hero-heading-sub h3 {
    font-size: 1.4rem;
    font-weight: 400;
    margin: 0;
    color: #ecf0f1;
    opacity: 0.9;
}

.hero-button-primary {
    margin-bottom: 15px;
    animation: fadeInLeft 1s ease-out 0.3s both;
}

.hero-button-secondary {
    animation: fadeInLeft 1s ease-out 0.4s both;
}

.hero-btn {
    display: inline-block;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
    min-width: 200px;
    text-align: center;
}

.hero-btn-primary {
    background: #e74c3c;
    color: #ffffff;
    border-color: #e74c3c;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.hero-btn-primary:hover {
    background: #c0392b;
    border-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    color: #ffffff;
}

.hero-btn-secondary {
    background: transparent;
    color: #ffffff;
    border-color: #ffffff;
}

.hero-btn-secondary:hover {
    background: #ffffff;
    color: #2c3e50;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ==========================================================================
   Search Section
   ========================================================================== */

.search-section {
    background: #f8f9fa;
    padding: 40px 0;
}

.search-widget {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.search-widget h2 {
    margin-bottom: 30px;
    color: #333;
    font-size: 1.8rem;
}

.search-input-group {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.search-input {
    flex: 1;
    padding: 15px 25px;
    border: none;
    font-size: 16px;
    outline: none;
}

.search-submit {
    background: #e74c3c;
    color: #fff;
    border: none;
    padding: 15px 30px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-submit:hover {
    background: #c0392b;
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: #e74c3c;
    color: #fff;
}

.btn-primary:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-secondary:hover {
    background: #fff;
    color: #333;
}

/* ==========================================================================
   USP Section
   ========================================================================== */

.usp-section {
    padding: 60px 0;
    background: #fff;
}

.usp-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 40px;
}

.usp-item {
    text-align: center;
    padding: 20px;
    transition: transform 0.3s ease;
}

.usp-item:hover {
    transform: translateY(-5px);
}

.usp-item img {
    width: 100%;
    max-width: 200px;
    height: auto;
    margin: 0 auto 20px;
}

.usp-item h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0;
    font-weight: 600;
}

/* ==========================================================================
   Showcase Section
   ========================================================================== */

.showcase-section {
    padding: 40px 0;
    background: #f8f9fa;
}

.showcase-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.showcase-images img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.showcase-images img:hover {
    transform: scale(1.05);
}

/* ==========================================================================
   About Section
   ========================================================================== */

.about-section {
    padding: 60px 0;
    background: #fff;
}

.about-features {
    list-style: none;
    margin: 20px 0;
    padding: 0;
}

.about-features li {
    padding: 10px 0;
    position: relative;
    padding-left: 25px;
}

.about-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #e74c3c;
    font-weight: bold;
}

.contact-form-widget {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
}

.homepage-contact-form .form-row {
    margin-bottom: 15px;
}

.homepage-contact-form .form-field input,
.homepage-contact-form .form-field textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.homepage-contact-form .form-field input:focus,
.homepage-contact-form .form-field textarea:focus {
    border-color: #e74c3c;
}

/* ==========================================================================
   Featured Occasions Section
   ========================================================================== */

.featured-occasions-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0;
}

.occasions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.occasion-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.occasion-card:hover {
    transform: translateY(-5px);
}

.occasion-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.occasion-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.occasion-content {
    padding: 25px;
}

.occasion-brand {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.occasion-model {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    line-height: 1.3;
}

.occasion-specs {
    margin-bottom: 20px;
}

.spec-item {
    display: inline-block;
    background: #f8f9fa;
    padding: 5px 10px;
    margin: 2px;
    border-radius: 15px;
    font-size: 12px;
    color: #666;
}

.occasion-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 20px;
}

/* ==========================================================================
   Financial Lease Section
   ========================================================================== */

.financial-lease-section {
    padding: 60px 0;
    background: #fff;
}

.lease-info-box {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.lease-info-box h3 {
    margin-bottom: 20px;
    color: #333;
}

/* ==========================================================================
   Testimonials Section
   ========================================================================== */

.testimonials-section {
    padding: 60px 0;
    background: #f8f9fa;
    text-align: center;
}

.testimonials-section h2 {
    margin-bottom: 20px;
    font-size: 2rem;
    color: #333;
}

.testimonials-intro {
    max-width: 600px;
    margin: 0 auto 40px;
    color: #666;
    font-size: 1.1rem;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.testimonial {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.testimonial:hover {
    transform: translateY(-5px);
}

.testimonial h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
}

.testimonial p {
    color: #666;
    font-style: italic;
    margin-bottom: 0;
}

.testimonial-cta {
    margin-top: 40px;
}

.testimonial-cta h3 {
    margin-bottom: 20px;
    color: #333;
}

/* ==========================================================================
   Footer Styles
   ========================================================================== */

.site-footer {
    background: #1a1a1a;
    color: #fff;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 20px;
    color: #e74c3c;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #e74c3c;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    text-align: center;
    color: #999;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .main-navigation {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .main-navigation.active {
        display: block;
    }

    .main-navigation ul {
        flex-direction: column;
        padding: 20px;
    }

    .main-navigation li {
        margin: 10px 0;
    }

    .hero-section {
        padding: 60px 0 80px 0;
    }

    .hero-inner {
        padding: 0 15px;
    }

    .hero-content-container {
        text-align: center;
        max-width: 100%;
    }

    .hero-heading-main h2 {
        font-size: 2.5rem;
    }

    .hero-heading-sub h3 {
        font-size: 1.2rem;
    }

    .hero-btn {
        display: block;
        margin: 0 auto 15px auto;
        max-width: 280px;
    }

    .usp-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .showcase-images {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .occasions-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .search-input-group {
        flex-direction: column;
        border-radius: 10px;
    }

    .search-input {
        border-radius: 10px 10px 0 0;
    }

    .search-submit {
        border-radius: 0 0 10px 10px;
    }

    .col-6,
    .col-4,
    .col-3 {
        flex: 0 0 100%;
    }

    .row {
        flex-direction: column;
    }
}

/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
    .hero-heading-main h2 {
        font-size: 3rem;
    }

    .hero-content-container {
        max-width: 500px;
    }

    .usp-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }

    .showcase-images {
        grid-template-columns: repeat(3, 1fr);
    }

    .occasions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Large Mobile Styles */
@media (max-width: 480px) {
    .hero-heading-main h2 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-heading-sub h3 {
        font-size: 1.1rem;
    }

    .hero-btn {
        padding: 12px 25px;
        font-size: 14px;
        min-width: 180px;
    }

    .search-input-group {
        margin: 0 10px;
    }

    .usp-item img {
        max-width: 150px;
    }
}

/* ==========================================================================
   Filter Styles
   ========================================================================== */

.occasions-filters {
    background: #f8f9fa;
    padding: 20px 0;
    margin-bottom: 30px;
}

.filter-toggle {
    text-align: center;
    margin-bottom: 20px;
}

.filter-panel {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: none;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.price-range,
.year-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.filter-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* ==========================================================================
   Results Styles
   ========================================================================== */

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.results-sorting {
    display: flex;
    align-items: center;
    gap: 10px;
}

.results-sorting select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* ==========================================================================
   Occasion Card Styles
   ========================================================================== */

.occasion-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.occasion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.occasion-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.occasion-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.occasion-card:hover .occasion-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #e74c3c;
    color: #fff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.occasion-content {
    padding: 25px;
}

.occasion-brand {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.occasion-model {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    line-height: 1.3;
}

.occasion-subtitle {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.occasion-price {
    margin-bottom: 20px;
}

.occasion-price .main-price {
    font-size: 24px;
    font-weight: bold;
    color: #e74c3c;
}

.occasion-price .rijklaar-price {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.occasion-specs {
    margin-bottom: 20px;
}

.spec-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-item i {
    width: 20px;
    color: #666;
    margin-right: 10px;
}

.spec-item span {
    font-size: 14px;
    color: #333;
}

.occasion-contact {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.occasion-contact p {
    margin-bottom: 10px;
    font-weight: 500;
}

.occasion-contact ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.occasion-contact li {
    margin-bottom: 5px;
}

.occasion-contact a {
    color: #e74c3c;
    font-size: 14px;
}

/* ==========================================================================
   Pagination Styles
   ========================================================================== */

.occasions-pagination {
    margin-top: 40px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 5px;
}

.pagination li {
    margin: 0;
}

.pagination a,
.pagination span {
    display: block;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover,
.pagination .current {
    background: #e74c3c;
    color: #fff;
    border-color: #e74c3c;
}

/* ==========================================================================
   WhatsApp Float Styles
   ========================================================================== */

.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: #25d366;
    border-radius: 50px;
    padding: 15px 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
}

.whatsapp-float i {
    font-size: 24px;
    margin-right: 10px;
}

.whatsapp-float span {
    font-size: 14px;
}

.whatsapp-float-occasion {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: #25d366;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    max-width: 250px;
}

.whatsapp-float-occasion a {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 10px;
}

.whatsapp-float-occasion i {
    font-size: 20px;
    margin-right: 8px;
}

.viewers-count {
    font-size: 12px;
    color: rgba(255,255,255,0.9);
    text-align: center;
}

.viewers-count .count {
    display: block;
    font-weight: bold;
    margin-top: 2px;
}

/* ==========================================================================
   Back to Top Styles
   ========================================================================== */

.back-to-top {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: #333;
    color: #fff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    z-index: 999;
    transition: all 0.3s ease;
}

.back-to-top:hover {
    background: #e74c3c;
    transform: translateY(-3px);
}

/* ==========================================================================
   Loading Styles
   ========================================================================== */

.loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Notification Styles
   ========================================================================== */

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: #fff;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #28a745;
}

.notification-error {
    background: #dc3545;
}

.notification-warning {
    background: #ffc107;
    color: #333;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

.hidden { display: none; }
.visible { display: block; }

.btn-block {
    width: 100%;
    display: block;
}

.btn-whatsapp {
    background: #25d366;
    color: #fff;
}

.btn-whatsapp:hover {
    background: #128c7e;
    color: #fff;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}
