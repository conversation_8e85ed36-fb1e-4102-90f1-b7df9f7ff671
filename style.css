/*
Theme Name: Autodebruin
Description: Op maat gemaakt WordPress-thema voor Autodebruin.nl - Bedrijfswagen specialist sinds 1960
Author: <PERSON> - Funnel Adviseur
Author URI: https://funneladviseur.nl
Version: 1.0.0
License: GPL v2 or later
Text Domain: autodebruin
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

a:hover {
    opacity: 0.8;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Layout & Grid System
   ========================================================================== */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333%; }
.col-2 { flex: 0 0 16.666%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* ==========================================================================
   Header Styles - Exact Autodebruin Recreation
   ========================================================================== */

.site-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Header Top Bar - Exact Match */
.header-top {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
}

.header-top-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-tagline-section {
    flex: 1;
}

.header-tagline-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    letter-spacing: 0.5px;
}

.header-contact-section {
    display: flex;
    align-items: center;
    gap: 30px;
}

.contact-info-list {
    display: flex;
    gap: 25px;
}

.contact-item a {
    display: flex;
    align-items: center;
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: #264F7F;
}

.contact-icon {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-icon svg {
    width: 16px;
    height: 16px;
    fill: #264F7F;
}

.contact-text {
    font-size: 14px;
}

.social-icons-wrapper {
    display: flex;
    gap: 8px;
}

.social-icon-item {
    display: inline-block;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #264F7F;
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background: #1e3f66;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(38, 79, 127, 0.3);
}

.social-icon svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Main Header - Exact Match */
.header-main {
    background: #fff;
    padding: 15px 0;
}

.header-main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-branding {
    flex-shrink: 0;
}

.site-logo {
    max-height: 60px;
    width: auto;
    transition: transform 0.3s ease;
}

.site-logo:hover {
    transform: scale(1.02);
}

/* Desktop Navigation - Elementor Style */
.main-navigation {
    display: flex;
    align-items: center;
}

.elementor-nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.elementor-nav-menu li {
    position: relative;
}

.elementor-nav-menu a {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    font-size: 15px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    text-transform: capitalize;
    position: relative;
    border-radius: 4px;
}

.elementor-nav-menu a:hover,
.elementor-nav-menu .current-menu-item a {
    background: rgba(38, 79, 127, 0.1);
    color: #264F7F;
}

/* Mobile Menu Toggle - Elementor Style */
.elementor-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    position: relative;
}

/* Desktop - Show navigation, hide toggle */
@media (min-width: 1025px) {
    .main-navigation {
        display: flex;
    }

    .elementor-menu-toggle {
        display: none;
    }

    .elementor-nav-menu--dropdown {
        display: none;
    }
}

.elementor-menu-toggle svg {
    width: 24px;
    height: 24px;
    fill: #2c3e50;
    transition: all 0.3s ease;
}

.elementor-menu-toggle__icon--close {
    display: none;
}

.elementor-menu-toggle.active .elementor-menu-toggle__icon--open {
    display: none;
}

.elementor-menu-toggle.active .elementor-menu-toggle__icon--close {
    display: block;
}

/* Mobile Dropdown Menu */
.elementor-nav-menu--dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-top: 1px solid #eee;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.elementor-nav-menu--dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.elementor-nav-menu--dropdown .elementor-nav-menu {
    flex-direction: column;
    gap: 0;
    padding: 20px;
}

.elementor-nav-menu--dropdown .elementor-nav-menu li {
    width: 100%;
    border-bottom: 1px solid #f0f0f0;
}

.elementor-nav-menu--dropdown .elementor-nav-menu a {
    display: block;
    padding: 15px 0;
    border-radius: 0;
}

.elementor-nav-menu--dropdown .elementor-nav-menu a:hover {
    background: none;
    color: #264F7F;
    padding-left: 10px;
}

/* Hide elements on mobile */
.elementor-hidden-mobile {
    display: block;
}

/* Sticky header */
.c-sticky-header {
    position: sticky;
    top: 0;
    z-index: 999;
}

/* Mobile Slide-out Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    height: 100vh;
    background: #fff;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 1002;
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.mobile-logo-img {
    max-height: 40px;
    width: auto;
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #2c3e50;
    cursor: pointer;
    padding: 5px;
    transition: color 0.3s ease;
}

.mobile-menu-close:hover {
    color: #264F7F;
}

.mobile-navigation {
    padding: 20px 0;
}

.mobile-nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-menu li {
    border-bottom: 1px solid #f0f0f0;
}

.mobile-nav-menu a {
    display: block;
    padding: 15px 20px;
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.3s ease;
    text-transform: capitalize;
}

.mobile-nav-menu a:hover,
.mobile-nav-menu .current-menu-item a {
    background: #f8f9fa;
    color: #264F7F;
    padding-left: 30px;
}

.mobile-menu-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.mobile-contact {
    margin-bottom: 20px;
}

.mobile-contact-item {
    display: flex;
    align-items: center;
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    margin-bottom: 10px;
    transition: color 0.3s ease;
}

.mobile-contact-item:hover {
    color: #264F7F;
}

.mobile-contact-item i {
    margin-right: 10px;
    font-size: 16px;
    color: #264F7F;
    width: 20px;
}

.mobile-social {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.mobile-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #264F7F;
    color: #fff;
    border-radius: 50%;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s ease;
}

.mobile-social a:hover {
    background: #1e3f66;
    transform: scale(1.1);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1001;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Body scroll lock when menu is open */
body.menu-open {
    overflow: hidden;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .elementor-hidden-mobile {
        display: none !important;
    }

    .elementor-menu-toggle {
        display: flex !important;
    }

    .main-navigation {
        display: none !important;
    }

    .header-main-container {
        padding: 0 15px;
    }

    .site-logo {
        max-height: 50px;
    }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .elementor-menu-toggle {
        display: flex !important;
    }

    .main-navigation {
        display: none !important;
    }

    .header-top-container,
    .header-main-container {
        padding: 0 15px;
    }

    .elementor-nav-menu {
        gap: 20px;
    }
}

/* Tablet adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
    .header-top-inner,
    .header-main-inner {
        padding: 0 15px;
    }

    .nav-menu {
        gap: 25px;
    }

    .nav-menu a {
        font-size: 14px;
    }
}

/* ==========================================================================
   Hero Section - Autodebruin Huisstijl
   ========================================================================== */

.hero-section {
    background: linear-gradient(rgba(38, 79, 127, 0.8), rgba(44, 62, 80, 0.8)), url('https://funnelsight.nl/wp-content/uploads/2025/05/autodebruin-bedrijfswagen-specialist.jpg');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    color: #ffffff;
    padding: 100px 0 120px 0;
    position: relative;
    overflow: hidden;
    min-height: 600px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(38, 79, 127, 0.1);
    z-index: 1;
}

.hero-inner {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 3;
}

.hero-content-container {
    max-width: 600px;
    text-align: left;
    animation: fadeInUp 1s ease-out;
}

.hero-heading-main {
    margin-bottom: 20px;
    animation: fadeInLeft 1s ease-out 0.1s both;
}

.hero-heading-main h2 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-heading-sub {
    margin-bottom: 40px;
    animation: fadeInLeft 1s ease-out 0.2s both;
}

.hero-heading-sub h3 {
    font-size: 1.4rem;
    font-weight: 400;
    margin: 0;
    color: #ecf0f1;
    opacity: 0.9;
}

.hero-button-primary {
    margin-bottom: 15px;
    animation: fadeInLeft 1s ease-out 0.3s both;
}

.hero-button-secondary {
    animation: fadeInLeft 1s ease-out 0.4s both;
}

.hero-btn {
    display: inline-block;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
    min-width: 200px;
    text-align: center;
}

.hero-btn-primary {
    background: #25D366;
    color: #ffffff;
    border-color: #25D366;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.hero-btn-primary:hover {
    background: #1DA851;
    border-color: #1DA851;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
    color: #ffffff;
}

.hero-btn-secondary {
    background: transparent;
    color: #ffffff;
    border-color: #ffffff;
}

.hero-btn-secondary:hover {
    background: #ffffff;
    color: #2c3e50;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ==========================================================================
   Search Section
   ========================================================================== */

.search-section {
    background: #f8f9fa;
    padding: 40px 0;
}

.search-widget {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.search-widget h2 {
    margin-bottom: 30px;
    color: #333;
    font-size: 1.8rem;
}

.search-input-group {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.search-input {
    flex: 1;
    padding: 15px 25px;
    border: none;
    font-size: 16px;
    outline: none;
}

.search-submit {
    background: #25D366;
    color: #fff;
    border: none;
    padding: 15px 30px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-submit:hover {
    background: #1DA851;
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    text-decoration: none;
}

/* Algemene primary buttons (formulieren) - blauw */
.btn-primary {
    background: #264F7F;
    color: #fff;
    border: 2px solid #264F7F;
}

.btn-primary:hover {
    background: #1e3f66;
    border-color: #1e3f66;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-secondary:hover {
    background: #fff;
    color: #333;
}

/* ==========================================================================
   USP Section - Verbeterde Versie
   ========================================================================== */

.usp-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
}

.usp-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(38,79,127,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
}

.usp-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 50px;
    position: relative;
    z-index: 2;
}

.usp-item {
    background: #ffffff;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.usp-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #264F7F 0%, #1e3f66 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.usp-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(38, 79, 127, 0.15);
}

.usp-item:hover::before {
    transform: scaleX(1);
}

.usp-image {
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.usp-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    transition: transform 0.4s ease;
}

.usp-item:hover .usp-image img {
    transform: scale(1.05);
}

.usp-content h3 {
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 700;
    line-height: 1.3;
}

.usp-content p {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
    font-weight: 400;
}

/* Hover effect for content */
.usp-item:hover .usp-content h3 {
    color: #25D366;
}

.usp-item:hover .usp-content p {
    color: #495057;
}

/* ==========================================================================
   Showcase Section
   ========================================================================== */

.showcase-section {
    padding: 40px 0;
    background: #f8f9fa;
}

.showcase-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.showcase-images img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.showcase-images img:hover {
    transform: scale(1.05);
}

/* ==========================================================================
   About Section
   ========================================================================== */

.about-section {
    padding: 60px 0;
    background: #fff;
}

.about-features {
    list-style: none;
    margin: 20px 0;
    padding: 0;
}

.about-features li {
    padding: 10px 0;
    position: relative;
    padding-left: 25px;
}

.about-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #25D366;
    font-weight: bold;
}

.contact-form-widget {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
}

.homepage-contact-form .form-row {
    margin-bottom: 15px;
}

.homepage-contact-form .form-field input,
.homepage-contact-form .form-field textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.homepage-contact-form .form-field input:focus,
.homepage-contact-form .form-field textarea:focus {
    border-color: #264F7F;
}

/* ==========================================================================
   Featured Occasions Section
   ========================================================================== */

.featured-occasions-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0;
}

.occasions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.occasion-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.occasion-card:hover {
    transform: translateY(-5px);
}

.occasion-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.occasion-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.occasion-content {
    padding: 25px;
}

/* Occasion Header - Merk en Model naast elkaar */
.occasion-header {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 15px;
}

.occasion-brand {
    font-size: 14px;
    color: #666;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.occasion-model {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.occasion-specs {
    margin-bottom: 20px;
}

.spec-item {
    display: inline-block;
    background: #f8f9fa;
    padding: 5px 10px;
    margin: 2px;
    border-radius: 15px;
    font-size: 12px;
    color: #666;
}

/* Occasion Mileage */
.occasion-mileage {
    font-size: 12px;
    color: #999;
    text-align: left;
    margin-bottom: 5px;
    font-weight: normal;
}

.occasion-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #25D366;
    margin-bottom: 20px;
}

/* Occasion Button - Enkele button */
.occasion-btn {
    display: block;
    width: 100%;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border-radius: 5px;
    text-align: center;
    transition: all 0.3s ease;
    background: #264F7F;
    color: #ffffff;
    border: 2px solid #264F7F;
}

.occasion-btn:hover {
    background: #1e3f66;
    border-color: #1e3f66;
    color: #ffffff;
    text-decoration: none;
}

/* Small occasion cards */
.occasion-price-small strong {
    color: #25D366;
    font-size: 1.2rem;
}

/* ==========================================================================
   Financial Lease Section
   ========================================================================== */

.financial-lease-section {
    padding: 60px 0;
    background: #fff;
}

.lease-info-box {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.lease-info-box h3 {
    margin-bottom: 20px;
    color: #333;
}

/* ==========================================================================
   Testimonials Section - Verbeterd met Carousel
   ========================================================================== */

.testimonials-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="testimonial-pattern" width="80" height="80" patternUnits="userSpaceOnUse"><path d="M40 20L60 40L40 60L20 40Z" fill="rgba(37,211,102,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23testimonial-pattern)"/></svg>');
    opacity: 0.4;
}

/* Header met icoon */
.testimonials-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.testimonials-icon {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.testimonials-icon-image {
    width: 80px;
    height: 80px;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
    transition: transform 0.3s ease;
}

.testimonials-icon-image:hover {
    transform: scale(1.05);
}

.testimonials-section h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 700;
}

.testimonials-intro {
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Carousel Container - Full Width */
.testimonials-carousel-container {
    position: relative;
    width: 100vw;
    margin: 0 0 60px 0;
    margin-left: calc(-50vw + 50%);
    z-index: 2;
    overflow: hidden;
}

.testimonials-carousel {
    position: relative;
    width: 100%;
    padding: 20px 0;
}

.testimonials-track {
    display: flex;
    gap: 30px;
    animation: testimonials-scroll 30s linear infinite;
    width: max-content;
}

.testimonials-track:hover {
    animation-play-state: paused;
}

.testimonial-slide {
    flex-shrink: 0;
    width: 350px;
    min-width: 350px;
}

/* Testimonial Cards */
.testimonial {
    background: #ffffff;
    padding: 25px;
    text-align: center;
    height: 320px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #25D366;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(37, 211, 102, 0.15);
}

/* Stars */
.testimonial-stars {
    display: flex;
    justify-content: center;
    gap: 3px;
    margin-bottom: 15px;
}

.testimonial-stars .star {
    width: 16px;
    height: 16px;
    filter: brightness(0) saturate(100%) invert(85%) sepia(95%) saturate(1945%) hue-rotate(348deg) brightness(103%) contrast(103%);
    transition: transform 0.2s ease;
}

.testimonial-stars .star:hover {
    transform: scale(1.1);
}

/* Testimonial Content */
.testimonial h4 {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
    line-height: 1.3;
}

.testimonial p {
    color: #6c757d;
    font-style: italic;
    line-height: 1.5;
    margin-bottom: 15px;
    flex-grow: 1;
    font-size: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.testimonial-author {
    border-top: 2px solid #e9ecef;
    padding-top: 12px;
    margin-top: auto;
}

.testimonial-author strong {
    display: block;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 1.2;
}

.testimonial-author span {
    color: #25D366;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
}

/* Carousel Controls */
.testimonials-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 -60px;
    pointer-events: none;
}

.carousel-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #ffffff;
    border: 2px solid #25D366;
    color: #25D366;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    pointer-events: all;
    position: relative;
    z-index: 3;
}

.carousel-btn:hover {
    background: #25D366;
    color: #ffffff;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.3);
}

.carousel-prev {
    left: -25px;
}

.carousel-next {
    right: -25px;
}

/* Carousel Dots */
.testimonials-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: #25D366;
    transform: scale(1.2);
}

/* CTA */
.testimonial-cta {
    text-align: center;
    padding: 40px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #25D366;
    position: relative;
    z-index: 2;
}

.testimonial-cta h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.3rem;
    font-weight: 600;
}

.testimonial-cta .btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
}

/* Testimonials Scroll Animation */
@keyframes testimonials-scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* ==========================================================================
   Process Section - Customer Journey
   ========================================================================== */

.process-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.process-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="process-pattern" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M30 10L50 30L30 50L10 30Z" fill="rgba(38,79,127,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23process-pattern)"/></svg>');
    opacity: 0.5;
}

/* Header */
.process-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.process-icon {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.process-icon i {
    font-size: 3rem;
    color: #25D366;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 50%;
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.15);
    transition: transform 0.3s ease;
}

.process-icon i:hover {
    transform: scale(1.05);
}

.process-section h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 700;
}

.process-intro {
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Timeline */
.process-timeline {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.process-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #25D366 0%, #1DA851 100%);
    transform: translateX(-50%);
    border-radius: 2px;
}

/* Process Steps */
.process-step {
    position: relative;
    margin-bottom: 80px;
    display: flex;
    align-items: center;
    gap: 40px;
}

.process-step:nth-child(odd) {
    justify-content: flex-end;
    padding-right: calc(50% + 60px);
}

.process-step:nth-child(even) {
    justify-content: flex-start;
    padding-left: calc(50% + 60px);
}

.process-step:nth-child(even) .step-content {
    text-align: left;
}

.step-number {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #25D366 0%, #1DA851 100%);
    color: #ffffff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    z-index: 3;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: #ffffff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.step-icon i {
    font-size: 1.8rem;
    color: #264F7F;
    transition: color 0.3s ease;
}

.process-step:hover .step-icon {
    border-color: #25D366;
    transform: scale(1.05);
}

.process-step:hover .step-icon i {
    color: #25D366;
}

.step-content {
    flex: 0 0 auto;
    max-width: 400px;
    width: 100%;
}

.step-content h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
    line-height: 1.3;
}

.step-content p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
    font-size: 15px;
}

/* CTA */
.process-cta {
    text-align: center;
    margin-top: 80px;
    padding: 50px 40px;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #25D366;
    position: relative;
    z-index: 2;
}

.process-cta h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5rem;
    font-weight: 600;
}

.process-cta p {
    color: #6c757d;
    margin-bottom: 30px;
    font-size: 1.1rem;
    line-height: 1.6;
}

.process-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.process-buttons .btn {
    min-width: 200px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 600;
}

.process-buttons .btn i {
    margin-right: 8px;
}

/* ==========================================================================
   About Section - Verbeterd
   ========================================================================== */

.about-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="lines" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M0 40L40 0M-10 10L10 -10M30 50L50 30" stroke="rgba(38,79,127,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23lines)"/></svg>');
    opacity: 0.5;
}

/* Hero Quote */
.about-hero {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.quote-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 50px 40px;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    position: relative;
}

.quote-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #25D366 0%, #1DA851 100%);
    border-radius: 2px;
}

.hero-quote {
    font-size: 1.8rem;
    line-height: 1.4;
    color: #2c3e50;
    font-style: italic;
    font-weight: 400;
    margin: 0 0 30px 0;
    position: relative;
}

.quote-mark {
    font-size: 3rem;
    color: #25D366;
    font-family: Georgia, serif;
    line-height: 1;
    opacity: 0.7;
}

.quote-author {
    font-style: normal;
    color: #25D366;
    font-weight: 600;
    font-size: 1.1rem;
}

.quote-title {
    display: block;
    color: #6c757d;
    font-weight: 400;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* About Content */
.about-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-wrap: wrap;
    gap: 60px;
    margin: 0 -15px;
}

.about-content .col-6 {
    flex: 0 0 calc(50% - 30px);
    padding: 0 15px;
}

.about-text-content {
    padding-right: 30px;
}

.about-text-content h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 700;
}

.about-intro {
    font-size: 1.2rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 40px;
}

.about-media-content {
    padding-left: 30px;
}

/* USP Grid */
.about-usp-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 60px;
    margin-top: 10px;
}

.about-usp-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.about-usp-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(38, 79, 127, 0.15);
}

.usp-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #25D366 0%, #1DA851 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
}

.usp-content h4 {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.usp-content p {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

/* About CTA */
.about-cta {
    text-align: center;
    padding: 40px;
    background: #ffffff;
    border-radius: 15px;
    border: 2px solid #e9ecef;
    color: #2c3e50;
}

.about-cta h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.contact-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-buttons .btn {
    min-width: 180px;
}

.contact-buttons .btn i {
    margin-right: 8px;
}

/* Telefoon button (blauw) */
.contact-buttons .btn-primary {
    background: #264F7F;
    color: #ffffff;
    border: 2px solid #264F7F;
}

.contact-buttons .btn-primary:hover {
    background: #1e3f66;
    border-color: #1e3f66;
    color: #ffffff;
}

/* Email button (wit met blauwe lijn) */
.btn-secondary {
    background: #ffffff;
    color: #264F7F;
    border: 2px solid #264F7F;
}

.btn-secondary:hover {
    background: #264F7F;
    border-color: #264F7F;
    color: #ffffff;
}

/* Video Container */
.video-container {
    margin-bottom: 50px;
    margin-top: 20px;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.video-caption {
    text-align: center;
    margin-top: 20px;
}

.video-caption h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.2rem;
    font-weight: 600;
}

.video-caption p {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

/* Contact Form Widget */
.contact-form-widget {
    background: #ffffff;
    padding: 35px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form-widget h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.3rem;
    text-align: center;
}

.homepage-contact-form .form-row {
    margin-bottom: 20px;
}

.homepage-contact-form .form-field input,
.homepage-contact-form .form-field textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.homepage-contact-form .form-field input:focus,
.homepage-contact-form .form-field textarea:focus {
    border-color: #25D366;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.btn-block {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
}

/* ==========================================================================
   Financial Lease Section - Verbeterd
   ========================================================================== */

.financial-lease-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
}

.financial-lease-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="lease-pattern" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(37,211,102,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23lease-pattern)"/></svg>');
    opacity: 0.6;
}

/* Lease Content Layout */
.lease-content {
    display: flex;
    gap: 80px;
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

.lease-text-content {
    flex: 1;
    padding-right: 40px;
}

.lease-visual-content {
    flex: 1;
    padding-left: 40px;
}

/* Typography */
.lease-text-content h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 700;
    line-height: 1.2;
}

.lease-text-content h3 {
    font-size: 1.3rem;
    color: #25D366;
    margin-bottom: 25px;
    font-weight: 600;
}

.lease-intro {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
    margin-bottom: 40px;
}

/* USP Grid */
.lease-usp-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-bottom: 50px;
}

.lease-usp-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #25D366;
}

.lease-usp-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(37, 211, 102, 0.15);
}

.lease-usp-item .usp-icon {
    flex-shrink: 0;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #25D366 0%, #1DA851 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 18px;
}

.lease-usp-item .usp-content h4 {
    font-size: 1rem;
    color: #2c3e50;
    margin-bottom: 6px;
    font-weight: 600;
}

.lease-usp-item .usp-content p {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

/* Contact CTA */
.lease-contact-cta {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #e9ecef;
    text-align: center;
}

.lease-contact-cta h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 600;
}

.lease-contact-cta .contact-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.lease-contact-cta .contact-buttons .btn {
    min-width: 160px;
    padding: 12px 20px;
}

.lease-contact-cta .contact-buttons .btn i {
    margin-right: 8px;
}

/* Visual Content */
.lease-image-container {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.lease-vehicle-image {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
    transition: transform 0.3s ease;
}

.lease-vehicle-image:hover {
    transform: scale(1.02);
}

/* Lease Info Box */
.lease-info-box {
    background: #ffffff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #25D366;
}

.lease-info-box h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600;
}

.lease-info-box p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.lease-info-box .btn {
    width: 100%;
    padding: 12px;
    font-weight: 600;
}

/* ==========================================================================
   Footer Styles
   ========================================================================== */

.site-footer {
    background: #1a1a1a;
    color: #fff;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 20px;
    color: #ffffff;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #264F7F;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    text-align: center;
    color: #999;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .main-navigation {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .main-navigation.active {
        display: block;
    }

    .main-navigation ul {
        flex-direction: column;
        padding: 20px;
    }

    .main-navigation li {
        margin: 10px 0;
    }

    .hero-section {
        padding: 60px 0 80px 0;
        background-attachment: scroll; /* Better performance on mobile */
        min-height: 500px;
    }

    .hero-inner {
        padding: 0 15px;
    }

    .hero-content-container {
        text-align: center;
        max-width: 100%;
    }

    .hero-heading-main h2 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-heading-sub h3 {
        font-size: 1.2rem;
    }

    .hero-btn {
        display: block;
        margin: 0 auto 15px auto;
        max-width: 280px;
    }

    .usp-section {
        padding: 60px 0;
    }

    .usp-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .usp-item {
        padding: 30px 20px;
    }

    .usp-image img {
        height: 180px;
    }

    .usp-content h3 {
        font-size: 1.2rem;
    }

    .usp-content p {
        font-size: 13px;
    }

    .showcase-images {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .occasions-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .search-input-group {
        flex-direction: column;
        border-radius: 10px;
    }

    .search-input {
        border-radius: 10px 10px 0 0;
    }

    .search-submit {
        border-radius: 0 0 10px 10px;
    }

    .col-6,
    .col-4,
    .col-3 {
        flex: 0 0 100%;
    }

    .row {
        flex-direction: column;
    }

    /* About Section Mobile */
    .about-section {
        padding: 60px 0;
    }

    .about-content {
        flex-direction: column;
        gap: 40px;
    }

    .about-content .col-6 {
        flex: 0 0 100%;
    }

    .about-text-content {
        padding-right: 0;
        margin-bottom: 30px;
    }

    .about-media-content {
        padding-left: 0;
    }

    .about-hero {
        margin-bottom: 50px;
    }

    .quote-container {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .hero-quote {
        font-size: 1.4rem;
    }

    .quote-mark {
        font-size: 2rem;
    }

    .about-text-content h2 {
        font-size: 2rem;
    }

    .about-intro {
        font-size: 1.1rem;
    }

    .about-usp-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 40px;
    }

    .about-usp-item {
        padding: 20px;
    }

    .about-cta {
        padding: 30px 20px;
    }

    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }

    .contact-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .video-container {
        margin-bottom: 30px;
        margin-top: 0;
    }

    .video-wrapper {
        border-radius: 10px;
    }

    .contact-form-widget {
        padding: 25px 20px;
    }

    /* Financial Lease Section Mobile */
    .financial-lease-section {
        padding: 60px 0;
    }

    .lease-content {
        flex-direction: column;
        gap: 40px;
    }

    .lease-text-content {
        padding-right: 0;
        order: 1;
    }

    .lease-visual-content {
        padding-left: 0;
        order: 2;
    }

    .lease-text-content h2 {
        font-size: 2rem;
    }

    .lease-intro {
        font-size: 1rem;
    }

    .lease-usp-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 40px;
    }

    .lease-usp-item {
        padding: 15px;
    }

    .lease-contact-cta {
        padding: 25px 20px;
    }

    .lease-contact-cta .contact-buttons {
        flex-direction: column;
        align-items: center;
    }

    .lease-contact-cta .contact-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .lease-image-container {
        margin-bottom: 30px;
    }

    .lease-info-box {
        padding: 25px 20px;
    }

    /* Testimonials Section Mobile */
    .testimonials-section {
        padding: 60px 0;
    }

    .testimonials-header {
        margin-bottom: 40px;
    }

    .testimonials-icon-image {
        width: 60px;
        height: 60px;
    }

    .testimonials-section h2 {
        font-size: 2rem;
    }

    .testimonials-intro {
        font-size: 1rem;
        padding: 0 15px;
    }

    .testimonials-carousel-container {
        width: 100vw;
        margin: 0 0 40px 0;
        margin-left: calc(-50vw + 50%);
    }

    .testimonial-slide {
        width: 280px;
        min-width: 280px;
    }

    .testimonial {
        padding: 20px 15px;
        height: 280px;
    }

    .testimonial h4 {
        font-size: 1rem;
        margin-bottom: 10px;
    }

    .testimonial p {
        font-size: 13px;
        margin-bottom: 12px;
        -webkit-line-clamp: 3;
    }

    .testimonial-stars {
        margin-bottom: 12px;
    }

    .testimonial-stars .star {
        width: 14px;
        height: 14px;
    }

    .testimonial-author {
        padding-top: 10px;
    }

    .testimonial-author strong {
        font-size: 13px;
        margin-bottom: 3px;
    }

    .testimonial-author span {
        font-size: 12px;
    }

    .testimonials-track {
        gap: 20px;
        animation-duration: 25s;
    }

    .testimonial-cta {
        padding: 30px 20px;
        margin: 0 15px;
    }

    /* Process Section Mobile */
    .process-section {
        padding: 60px 0;
    }

    .process-header {
        margin-bottom: 50px;
    }

    .process-icon i {
        font-size: 2.5rem;
        padding: 20px;
    }

    .process-section h2 {
        font-size: 2rem;
    }

    .process-intro {
        font-size: 1rem;
        padding: 0 15px;
    }

    .process-timeline {
        padding: 0 15px;
    }

    .process-timeline::before {
        left: 30px;
        transform: none;
    }

    .process-step {
        flex-direction: column !important;
        align-items: flex-start;
        margin-bottom: 50px;
        padding: 0 15px 0 70px !important; /* Override desktop padding */
        justify-content: flex-start !important; /* Override desktop justify-content */
    }

    .process-step:nth-child(odd) {
        padding-right: 15px !important;
        padding-left: 70px !important;
    }

    .process-step:nth-child(even) {
        padding-left: 70px !important;
        padding-right: 15px !important;
    }

    .process-step:nth-child(even) .step-content {
        text-align: left !important;
    }

    .step-number {
        left: 30px;
        top: 20px; /* Vaste positie bovenaan */
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;
    }

    .step-icon i {
        font-size: 1.4rem;
    }

    .step-content {
        max-width: none;
        width: 100%;
        margin-top: 15px;
    }

    .step-content h3 {
        font-size: 1.1rem;
        margin-bottom: 10px;
        line-height: 1.3;
    }

    .step-content p {
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
    }

    .process-cta {
        margin-top: 50px;
        padding: 30px 20px;
        margin-left: 15px;
        margin-right: 15px;
    }

    .process-cta h3 {
        font-size: 1.3rem;
    }

    .process-cta p {
        font-size: 1rem;
    }

    .process-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .process-buttons .btn {
        min-width: 250px;
    }
}

/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
    .hero-heading-main h2 {
        font-size: 3rem;
    }

    .hero-content-container {
        max-width: 500px;
    }

    .usp-section {
        padding: 70px 0;
    }

    .usp-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 35px;
    }

    .usp-item {
        padding: 35px 25px;
    }

    .usp-image img {
        height: 180px;
    }

    .usp-content h3 {
        font-size: 1.3rem;
    }

    .showcase-images {
        grid-template-columns: repeat(3, 1fr);
    }

    .occasions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* About Section Tablet */
    .about-section {
        padding: 80px 0;
    }

    .about-content {
        gap: 50px;
        display: flex;
        flex-direction: row;
    }

    .about-content .col-6 {
        flex: 0 0 calc(50% - 25px);
    }

    .about-text-content {
        padding-right: 20px;
    }

    .about-media-content {
        padding-left: 20px;
    }

    .quote-container {
        padding: 40px 30px;
    }

    .hero-quote {
        font-size: 1.6rem;
    }

    .about-text-content h2 {
        font-size: 2.2rem;
    }

    .about-usp-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-bottom: 50px;
    }

    .contact-buttons {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .video-container {
        margin-bottom: 40px;
    }

    /* Financial Lease Section Tablet */
    .financial-lease-section {
        padding: 80px 0;
    }

    .lease-content {
        gap: 60px;
    }

    .lease-text-content {
        padding-right: 20px;
    }

    .lease-visual-content {
        padding-left: 20px;
    }

    .lease-text-content h2 {
        font-size: 2.2rem;
    }

    .lease-usp-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .lease-contact-cta .contact-buttons {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    /* Testimonials Section Tablet */
    .testimonials-section {
        padding: 80px 0;
    }

    .testimonials-header {
        margin-bottom: 50px;
    }

    .testimonials-icon-image {
        width: 70px;
        height: 70px;
    }

    .testimonials-section h2 {
        font-size: 2.2rem;
    }

    .testimonials-carousel-container {
        width: 100vw;
        margin: 0 0 50px 0;
        margin-left: calc(-50vw + 50%);
    }

    .testimonial-slide {
        width: 320px;
        min-width: 320px;
    }

    .testimonial {
        padding: 22px 20px;
        height: 300px;
    }

    .testimonial h4 {
        font-size: 1.05rem;
        margin-bottom: 11px;
    }

    .testimonial p {
        font-size: 13px;
        margin-bottom: 13px;
    }

    .testimonial-stars {
        margin-bottom: 13px;
    }

    .testimonial-stars .star {
        width: 15px;
        height: 15px;
    }

    .testimonial-author {
        padding-top: 11px;
    }

    .testimonial-author strong {
        font-size: 13px;
        margin-bottom: 3px;
    }

    .testimonial-author span {
        font-size: 12px;
    }

    .testimonials-track {
        gap: 25px;
        animation-duration: 28s;
    }

    .testimonial-cta {
        padding: 35px 25px;
        margin: 0 30px;
    }

    /* Process Section Tablet */
    .process-section {
        padding: 80px 0;
    }

    .process-header {
        margin-bottom: 60px;
    }

    .process-icon i {
        font-size: 2.8rem;
        padding: 22px;
    }

    .process-section h2 {
        font-size: 2.2rem;
    }

    .process-intro {
        font-size: 1.05rem;
        padding: 0 30px;
    }

    .process-timeline {
        padding: 0 30px;
    }

    .process-step {
        margin-bottom: 60px;
        gap: 30px;
    }

    .process-step:nth-child(odd) {
        padding-right: calc(50% + 50px);
        padding-left: 0;
    }

    .process-step:nth-child(even) {
        padding-left: calc(50% + 50px);
        padding-right: 0;
    }

    .step-number {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .step-icon {
        width: 70px;
        height: 70px;
    }

    .step-icon i {
        font-size: 1.6rem;
    }

    .step-content {
        max-width: 320px;
    }

    .step-content h3 {
        font-size: 1.2rem;
        margin-bottom: 11px;
    }

    .step-content p {
        font-size: 14px;
    }

    .process-cta {
        margin-top: 60px;
        padding: 40px 30px;
        margin-left: 30px;
        margin-right: 30px;
    }

    .process-cta h3 {
        font-size: 1.4rem;
    }

    .process-buttons .btn {
        min-width: 180px;
    }
}

/* Large Mobile Styles */
@media (max-width: 480px) {
    .hero-section {
        min-height: 400px;
        padding: 40px 0 60px 0;
    }

    .hero-heading-main h2 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-heading-sub h3 {
        font-size: 1.1rem;
    }

    .hero-btn {
        padding: 12px 25px;
        font-size: 14px;
        min-width: 180px;
    }

    .search-input-group {
        margin: 0 10px;
    }

    .usp-item img {
        max-width: 150px;
    }

    .site-logo {
        max-height: 40px;
    }

    .mobile-logo-img {
        max-height: 35px;
    }
}

/* ==========================================================================
   Filter Styles
   ========================================================================== */

.occasions-filters {
    background: #f8f9fa;
    padding: 20px 0;
    margin-bottom: 30px;
}

.filter-toggle {
    text-align: center;
    margin-bottom: 20px;
}

.filter-panel {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: none;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.price-range,
.year-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.filter-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* ==========================================================================
   Results Styles
   ========================================================================== */

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.results-sorting {
    display: flex;
    align-items: center;
    gap: 10px;
}

.results-sorting select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* ==========================================================================
   Occasion Card Styles
   ========================================================================== */

.occasion-card {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.occasion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.occasion-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.occasion-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.occasion-card:hover .occasion-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #264F7F;
    color: #fff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.occasion-content {
    padding: 25px;
}

.occasion-brand {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.occasion-model {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    line-height: 1.3;
}

.occasion-subtitle {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.occasion-price {
    margin-bottom: 20px;
}

.occasion-price .main-price {
    font-size: 24px;
    font-weight: bold;
    color: #264F7F;
}

.occasion-price .rijklaar-price {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.occasion-specs {
    margin-bottom: 20px;
}

.spec-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-item i {
    width: 20px;
    color: #666;
    margin-right: 10px;
}

.spec-item span {
    font-size: 14px;
    color: #333;
}

.occasion-contact {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.occasion-contact p {
    margin-bottom: 10px;
    font-weight: 500;
}

.occasion-contact ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.occasion-contact li {
    margin-bottom: 5px;
}

.occasion-contact a {
    color: #264F7F;
    font-size: 14px;
}

/* ==========================================================================
   Pagination Styles
   ========================================================================== */

.occasions-pagination {
    margin-top: 40px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 5px;
}

.pagination li {
    margin: 0;
}

.pagination a,
.pagination span {
    display: block;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover,
.pagination .current {
    background: #264F7F;
    color: #fff;
    border-color: #264F7F;
}

/* ==========================================================================
   WhatsApp Float Styles
   ========================================================================== */

.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: #25d366;
    border-radius: 50px;
    padding: 15px 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
}

.whatsapp-float i {
    font-size: 24px;
    margin-right: 10px;
}

.whatsapp-float span {
    font-size: 14px;
}

.whatsapp-float-occasion {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: #25d366;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    max-width: 250px;
}

.whatsapp-float-occasion a {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 10px;
}

.whatsapp-float-occasion i {
    font-size: 20px;
    margin-right: 8px;
}

.viewers-count {
    font-size: 12px;
    color: rgba(255,255,255,0.9);
    text-align: center;
}

.viewers-count .count {
    display: block;
    font-weight: bold;
    margin-top: 2px;
}

/* ==========================================================================
   Back to Top Styles
   ========================================================================== */

.back-to-top {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: #333;
    color: #fff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    z-index: 999;
    transition: all 0.3s ease;
}

.back-to-top:hover {
    background: #264F7F;
    transform: translateY(-3px);
}

/* ==========================================================================
   Loading Styles
   ========================================================================== */

.loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #264F7F;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Notification Styles
   ========================================================================== */

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: #fff;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #28a745;
}

.notification-error {
    background: #dc3545;
}

.notification-warning {
    background: #ffc107;
    color: #333;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

.hidden { display: none; }
.visible { display: block; }

.btn-block {
    width: 100%;
    display: block;
}

.btn-whatsapp {
    background: #25d366;
    color: #fff;
}

.btn-whatsapp:hover {
    background: #128c7e;
    color: #fff;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}
