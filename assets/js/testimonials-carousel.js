/**
 * Testimonials Carousel JavaScript
 * Handles smooth carousel functionality with auto-play and manual controls
 */

(function($) {
    'use strict';

    // Carousel settings
    const CAROUSEL_SETTINGS = {
        autoPlay: true,
        autoPlayInterval: 5000,
        transitionDuration: 500,
        pauseOnHover: true,
        swipeEnabled: true
    };

    let currentSlide = 0;
    let totalSlides = 0;
    let autoPlayTimer = null;
    let isTransitioning = false;
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    // Initialize when document is ready
    $(document).ready(function() {
        initTestimonialsCarousel();
    });

    /**
     * Initialize testimonials carousel
     */
    function initTestimonialsCarousel() {
        const carousel = $('.testimonials-carousel');
        
        if (!carousel.length) {
            return;
        }

        // Setup carousel
        setupCarousel();
        
        // Bind events
        bindCarouselEvents();
        
        // Start auto-play
        if (CAROUSEL_SETTINGS.autoPlay) {
            startAutoPlay();
        }

        // Add touch/swipe support
        if (CAROUSEL_SETTINGS.swipeEnabled) {
            addSwipeSupport();
        }
    }

    /**
     * Setup carousel initial state
     */
    function setupCarousel() {
        const slides = $('.testimonial-slide');
        totalSlides = slides.length;
        
        if (totalSlides === 0) {
            return;
        }

        // Set initial position
        updateCarouselPosition(0);
        
        // Update dots
        updateDots(0);
        
        // Show controls if more than 1 slide
        if (totalSlides > 1) {
            $('.testimonials-controls').show();
            $('.testimonials-dots').show();
        } else {
            $('.testimonials-controls').hide();
            $('.testimonials-dots').hide();
        }
    }

    /**
     * Bind carousel events
     */
    function bindCarouselEvents() {
        // Previous button
        $('.carousel-prev').on('click', function(e) {
            e.preventDefault();
            if (!isTransitioning) {
                goToPreviousSlide();
            }
        });

        // Next button
        $('.carousel-next').on('click', function(e) {
            e.preventDefault();
            if (!isTransitioning) {
                goToNextSlide();
            }
        });

        // Dot navigation
        $('.testimonials-dots .dot').on('click', function(e) {
            e.preventDefault();
            if (!isTransitioning) {
                const slideIndex = parseInt($(this).data('slide'));
                goToSlide(slideIndex);
            }
        });

        // Pause on hover
        if (CAROUSEL_SETTINGS.pauseOnHover) {
            $('.testimonials-carousel-container').on('mouseenter', function() {
                pauseAutoPlay();
            }).on('mouseleave', function() {
                if (CAROUSEL_SETTINGS.autoPlay) {
                    startAutoPlay();
                }
            });
        }

        // Keyboard navigation
        $(document).on('keydown', function(e) {
            if ($('.testimonials-carousel-container:hover').length) {
                if (e.keyCode === 37) { // Left arrow
                    e.preventDefault();
                    if (!isTransitioning) {
                        goToPreviousSlide();
                    }
                } else if (e.keyCode === 39) { // Right arrow
                    e.preventDefault();
                    if (!isTransitioning) {
                        goToNextSlide();
                    }
                }
            }
        });
    }

    /**
     * Add touch/swipe support
     */
    function addSwipeSupport() {
        const carousel = $('.testimonials-carousel')[0];
        
        if (!carousel) {
            return;
        }

        // Touch events
        carousel.addEventListener('touchstart', handleTouchStart, { passive: true });
        carousel.addEventListener('touchmove', handleTouchMove, { passive: false });
        carousel.addEventListener('touchend', handleTouchEnd, { passive: true });

        // Mouse events for desktop dragging
        carousel.addEventListener('mousedown', handleMouseDown);
        carousel.addEventListener('mousemove', handleMouseMove);
        carousel.addEventListener('mouseup', handleMouseUp);
        carousel.addEventListener('mouseleave', handleMouseUp);
    }

    /**
     * Handle touch/mouse start
     */
    function handleTouchStart(e) {
        startX = e.touches ? e.touches[0].clientX : e.clientX;
        isDragging = true;
        pauseAutoPlay();
    }

    function handleMouseDown(e) {
        e.preventDefault();
        handleTouchStart(e);
    }

    /**
     * Handle touch/mouse move
     */
    function handleTouchMove(e) {
        if (!isDragging) return;
        
        e.preventDefault();
        currentX = e.touches ? e.touches[0].clientX : e.clientX;
    }

    function handleMouseMove(e) {
        if (!isDragging) return;
        handleTouchMove(e);
    }

    /**
     * Handle touch/mouse end
     */
    function handleTouchEnd() {
        if (!isDragging) return;
        
        isDragging = false;
        
        const diffX = startX - currentX;
        const threshold = 50; // Minimum swipe distance
        
        if (Math.abs(diffX) > threshold && !isTransitioning) {
            if (diffX > 0) {
                // Swiped left - go to next slide
                goToNextSlide();
            } else {
                // Swiped right - go to previous slide
                goToPreviousSlide();
            }
        }
        
        if (CAROUSEL_SETTINGS.autoPlay) {
            startAutoPlay();
        }
    }

    function handleMouseUp() {
        handleTouchEnd();
    }

    /**
     * Go to next slide
     */
    function goToNextSlide() {
        const nextSlide = (currentSlide + 1) % totalSlides;
        goToSlide(nextSlide);
    }

    /**
     * Go to previous slide
     */
    function goToPreviousSlide() {
        const prevSlide = currentSlide === 0 ? totalSlides - 1 : currentSlide - 1;
        goToSlide(prevSlide);
    }

    /**
     * Go to specific slide
     */
    function goToSlide(slideIndex) {
        if (slideIndex === currentSlide || isTransitioning) {
            return;
        }

        isTransitioning = true;
        currentSlide = slideIndex;

        // Update carousel position
        updateCarouselPosition(slideIndex);
        
        // Update dots
        updateDots(slideIndex);

        // Reset transition flag after animation
        setTimeout(function() {
            isTransitioning = false;
        }, CAROUSEL_SETTINGS.transitionDuration);

        // Restart auto-play timer
        if (CAROUSEL_SETTINGS.autoPlay) {
            restartAutoPlay();
        }
    }

    /**
     * Update carousel position
     */
    function updateCarouselPosition(slideIndex) {
        const track = $('.testimonials-track');
        const translateX = -(slideIndex * (100 / totalSlides));
        
        track.css('transform', `translateX(${translateX}%)`);
    }

    /**
     * Update dot indicators
     */
    function updateDots(slideIndex) {
        $('.testimonials-dots .dot').removeClass('active');
        $('.testimonials-dots .dot').eq(slideIndex).addClass('active');
    }

    /**
     * Start auto-play
     */
    function startAutoPlay() {
        if (totalSlides <= 1) {
            return;
        }

        pauseAutoPlay(); // Clear any existing timer
        
        autoPlayTimer = setInterval(function() {
            if (!isTransitioning && !isDragging) {
                goToNextSlide();
            }
        }, CAROUSEL_SETTINGS.autoPlayInterval);
    }

    /**
     * Pause auto-play
     */
    function pauseAutoPlay() {
        if (autoPlayTimer) {
            clearInterval(autoPlayTimer);
            autoPlayTimer = null;
        }
    }

    /**
     * Restart auto-play
     */
    function restartAutoPlay() {
        if (CAROUSEL_SETTINGS.autoPlay) {
            pauseAutoPlay();
            startAutoPlay();
        }
    }

    /**
     * Handle window resize
     */
    $(window).on('resize', function() {
        // Recalculate positions on resize
        setTimeout(function() {
            updateCarouselPosition(currentSlide);
        }, 100);
    });

    /**
     * Handle visibility change (pause when tab is not active)
     */
    $(document).on('visibilitychange', function() {
        if (document.hidden) {
            pauseAutoPlay();
        } else if (CAROUSEL_SETTINGS.autoPlay) {
            startAutoPlay();
        }
    });

    // Expose functions for external use
    window.testimonialsCarousel = {
        goToSlide: goToSlide,
        goToNext: goToNextSlide,
        goToPrevious: goToPreviousSlide,
        startAutoPlay: startAutoPlay,
        pauseAutoPlay: pauseAutoPlay,
        getCurrentSlide: function() { return currentSlide; },
        getTotalSlides: function() { return totalSlides; }
    };

})(jQuery);
