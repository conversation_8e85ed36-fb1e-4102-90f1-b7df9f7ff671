/**
 * Main JavaScript file for Autodebruin theme
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {

        // Initialize AOS (Animate On Scroll)
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100,
                delay: 0
            });
        }

        // Initialize all functions
        initMobileMenu();
        initOccasionFilters();
        initLeaseCalculator();
        initContactForms();
        initImageGallery();
        initScrollToTop();
        initWhatsAppFloat();
        initSearchSuggestions();
        initLazyLoading();

    });

    /**
     * Mobile Menu Toggle - Elementor Style
     */
    function initMobileMenu() {
        // Elementor menu toggle
        $('.elementor-menu-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('.elementor-nav-menu--dropdown').toggleClass('active');
            $('body').toggleClass('menu-open');

            // Update aria-expanded
            var isExpanded = $(this).hasClass('active');
            $(this).attr('aria-expanded', isExpanded);
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.header-main').length) {
                closeElementorMenu();
            }
        });

        // Close menu when clicking menu links
        $('.elementor-nav-menu--dropdown a').on('click', function() {
            closeElementorMenu();
        });

        // Close menu with escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('.elementor-nav-menu--dropdown').hasClass('active')) {
                closeElementorMenu();
            }
        });

        function closeElementorMenu() {
            $('.elementor-menu-toggle').removeClass('active');
            $('.elementor-nav-menu--dropdown').removeClass('active');
            $('body').removeClass('menu-open');
            $('.elementor-menu-toggle').attr('aria-expanded', false);
        }

        // Handle window resize
        $(window).on('resize', function() {
            if ($(window).width() > 1024) {
                closeElementorMenu();
            }
        });
    }

    /**
     * Occasion Filters
     */
    function initOccasionFilters() {
        // Filter toggle
        $('#filter-toggle-btn').on('click', function() {
            $('#filter-panel').slideToggle();
            $(this).toggleClass('active');
        });

        // Filter form submission
        $('#occasions-filter-form').on('submit', function(e) {
            e.preventDefault();
            filterOccasions(1);
        });

        // Sorting change
        $('#sort-occasions').on('change', function() {
            filterOccasions(1);
        });

        // Reset filters
        $('#occasions-filter-form button[type="reset"]').on('click', function() {
            $('#occasions-filter-form')[0].reset();
            filterOccasions(1);
        });

        // Pagination clicks
        $(document).on('click', '.occasions-pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').match(/paged=(\d+)/);
            if (page) {
                filterOccasions(page[1]);
            }
        });
    }

    /**
     * Filter occasions via AJAX
     */
    function filterOccasions(page) {
        var formData = $('#occasions-filter-form').serialize();
        var sortBy = $('#sort-occasions').val();

        if (sortBy) {
            formData += '&orderby=' + sortBy;
        }

        formData += '&paged=' + page;
        formData += '&action=filter_occasions';
        formData += '&nonce=' + autodebruin_ajax.nonce;

        // Show loading
        $('#occasions-grid').addClass('loading');

        $.post(autodebruin_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                updateOccasionsGrid(response.data);
                updateResultsCount(response.data.total);
                updatePagination(response.data);
            }
        }).always(function() {
            $('#occasions-grid').removeClass('loading');
        });
    }

    /**
     * Update occasions grid
     */
    function updateOccasionsGrid(data) {
        var html = '';

        if (data.occasions.length > 0) {
            $.each(data.occasions, function(index, occasion) {
                html += buildOccasionCard(occasion);
            });
        } else {
            html = '<div class="no-occasions"><h3>Geen occasions gevonden</h3><p>Er zijn geen occasions die voldoen aan uw zoekcriteria.</p></div>';
        }

        $('#occasions-grid').html(html);

        // Scroll to results
        $('html, body').animate({
            scrollTop: $('#occasions-grid').offset().top - 100
        }, 500);
    }

    /**
     * Build occasion card HTML
     */
    function buildOccasionCard(occasion) {
        var html = '<article class="occasion-card">';
        html += '<div class="occasion-image">';
        html += '<a href="' + occasion.permalink + '">';
        html += occasion.thumbnail || '<img src="' + autodebruin_ajax.placeholder_image + '" alt="' + occasion.title + '">';
        html += '</a>';
        if (occasion.featured) {
            html += '<div class="featured-badge"><span>Uitgelicht</span></div>';
        }
        html += '</div>';

        html += '<div class="occasion-content">';
        html += '<div class="occasion-header">';
        html += '<h3 class="occasion-brand"><a href="' + occasion.permalink + '">' + (occasion.specs.merk || '') + '</a></h3>';
        html += '<h4 class="occasion-model"><a href="' + occasion.permalink + '">' + occasion.title + '</a></h4>';
        html += '<p class="occasion-subtitle"><a href="' + occasion.permalink + '">' + occasion.excerpt + '</a></p>';
        html += '</div>';

        if (occasion.specs.price) {
            html += '<div class="occasion-price">';
            html += '<div class="main-price">Nu voor: <strong>€ ' + parseInt(occasion.specs.price).toLocaleString('nl-NL') + ',-</strong></div>';
            html += '</div>';
        }

        html += '<div class="occasion-specs">';
        if (occasion.specs.year) {
            html += '<div class="spec-item"><a href="' + occasion.permalink + '"><i class="fas fa-calendar"></i><span>Bouwjaar: ' + occasion.specs.year + '</span></a></div>';
        }
        if (occasion.specs.kilometers) {
            html += '<div class="spec-item"><a href="' + occasion.permalink + '"><i class="fas fa-tachometer-alt"></i><span>Kilometers: ' + parseInt(occasion.specs.kilometers).toLocaleString('nl-NL') + ' km</span></a></div>';
        }
        if (occasion.specs.brandstof) {
            html += '<div class="spec-item"><a href="' + occasion.permalink + '"><i class="fas fa-gas-pump"></i><span>Brandstof: ' + occasion.specs.brandstof + '</span></a></div>';
        }
        if (occasion.specs.transmissie) {
            html += '<div class="spec-item"><a href="' + occasion.permalink + '"><i class="fas fa-cogs"></i><span>Transmissie: ' + occasion.specs.transmissie + '</span></a></div>';
        }
        html += '</div>';

        html += '<div class="occasion-actions">';
        html += '<a href="' + occasion.permalink + '" class="btn btn-primary btn-block">Bekijk auto</a>';
        html += '</div>';

        html += '</div>';
        html += '</article>';

        return html;
    }

    /**
     * Update results count
     */
    function updateResultsCount(total) {
        var text = total === 1 ? total + ' Occasion beschikbaar' : total + ' Occasions beschikbaar';
        $('.results-count').text(text);
    }

    /**
     * Update pagination
     */
    function updatePagination(data) {
        // This would need to be implemented based on the pagination structure
        // For now, we'll hide pagination if there's only one page
        if (data.max_pages <= 1) {
            $('.occasions-pagination').hide();
        } else {
            $('.occasions-pagination').show();
            // Update pagination links here
        }
    }

    /**
     * Lease Calculator
     */
    function initLeaseCalculator() {
        $('#lease-calculator input, #lease-calculator select').on('change keyup', function() {
            calculateLease();
        });

        $('#request-quote').on('click', function() {
            // Open contact form or redirect to contact page
            window.location.href = '/contact/';
        });
    }

    /**
     * Calculate lease payment
     */
    function calculateLease() {
        var vehiclePrice = parseFloat($('#vehicle-price').val()) || 0;
        var downPayment = parseFloat($('#down-payment').val()) || 0;
        var leaseTerm = parseInt($('#lease-term').val()) || 36;
        var residualValue = parseFloat($('#residual-value').val()) || 0;

        if (vehiclePrice > 0) {
            $.post(autodebruin_ajax.ajax_url, {
                action: 'calculate_lease',
                vehicle_price: vehiclePrice,
                down_payment: downPayment,
                lease_term: leaseTerm,
                residual_value: residualValue,
                nonce: autodebruin_ajax.nonce
            }, function(response) {
                if (response.success) {
                    $('#monthly-amount').text('€ ' + Math.round(response.monthly_payment).toLocaleString('nl-NL'));
                }
            });
        }
    }

    /**
     * Contact Forms
     */
    function initContactForms() {
        // Homepage contact form
        $('.homepage-contact-form').on('submit', function(e) {
            e.preventDefault();

            var form = $(this);
            var formData = form.serialize();
            formData += '&action=submit_contact_form';
            formData += '&nonce=' + autodebruin_ajax.nonce;

            // Show loading
            form.find('button[type="submit"]').prop('disabled', true).text('Verzenden...');

            $.post(autodebruin_ajax.ajax_url, formData, function(response) {
                if (response.success) {
                    form[0].reset();
                    showNotification('Uw bericht is verzonden!', 'success');
                } else {
                    showNotification(response.data || 'Er is een fout opgetreden.', 'error');
                }
            }).always(function() {
                form.find('button[type="submit"]').prop('disabled', false).text('Verzenden');
            });
        });

        // General contact forms
        $('.contact-form').on('submit', function(e) {
            e.preventDefault();

            var form = $(this);
            var formData = form.serialize();
            formData += '&action=submit_contact_form';
            formData += '&nonce=' + autodebruin_ajax.nonce;

            // Show loading
            form.find('button[type="submit"]').prop('disabled', true).text('Verzenden...');

            $.post(autodebruin_ajax.ajax_url, formData, function(response) {
                if (response.success) {
                    form[0].reset();
                    showNotification('Uw bericht is verzonden!', 'success');
                } else {
                    showNotification(response.data || 'Er is een fout opgetreden.', 'error');
                }
            }).always(function() {
                form.find('button[type="submit"]').prop('disabled', false).text('Verzenden');
            });
        });
    }

    /**
     * Image Gallery
     */
    function initImageGallery() {
        $('.gallery-thumb').on('click', function() {
            var newSrc = $(this).attr('src').replace('-thumbnail', '-large');
            $('.occasion-main-image').attr('src', newSrc);
            $('.gallery-thumb').removeClass('active');
            $(this).addClass('active');
        });

        // Lightbox functionality could be added here
    }

    /**
     * Scroll to Top
     */
    function initScrollToTop() {
        var backToTop = $('#back-to-top');

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTop.fadeIn();
            } else {
                backToTop.fadeOut();
            }
        });

        backToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 600);
            return false;
        });
    }

    /**
     * WhatsApp Float
     */
    function initWhatsAppFloat() {
        // Add animation or interaction for WhatsApp button
        $('.whatsapp-float').hover(
            function() {
                $(this).addClass('hover');
            },
            function() {
                $(this).removeClass('hover');
            }
        );
    }

    /**
     * Search Suggestions
     */
    function initSearchSuggestions() {
        var searchInput = $('.search-field');
        var suggestionsContainer = $('<div class="search-suggestions"></div>');

        searchInput.after(suggestionsContainer);

        searchInput.on('keyup', function() {
            var term = $(this).val();

            if (term.length >= 2) {
                $.post(autodebruin_ajax.ajax_url, {
                    action: 'search_suggestions',
                    term: term
                }, function(response) {
                    if (response.success && response.data.length > 0) {
                        var html = '<ul>';
                        $.each(response.data, function(index, suggestion) {
                            html += '<li><a href="' + suggestion.url + '">' + suggestion.label + '</a></li>';
                        });
                        html += '</ul>';
                        suggestionsContainer.html(html).show();
                    } else {
                        suggestionsContainer.hide();
                    }
                });
            } else {
                suggestionsContainer.hide();
            }
        });

        // Hide suggestions when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-field, .search-suggestions').length) {
                suggestionsContainer.hide();
            }
        });
    }

    /**
     * Lazy Loading
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        var notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
        $('body').append(notification);

        setTimeout(function() {
            notification.addClass('show');
        }, 100);

        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }

    /**
     * Smooth scrolling for anchor links
     */
    $('a[href^="#"]').on('click', function(e) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 600);
        }
    });

    /**
     * Form validation
     */
    function validateForm(form) {
        var isValid = true;

        form.find('[required]').each(function() {
            var field = $(this);
            var value = field.val().trim();

            if (!value) {
                field.addClass('error');
                isValid = false;
            } else {
                field.removeClass('error');
            }

            // Email validation
            if (field.attr('type') === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    field.addClass('error');
                    isValid = false;
                }
            }
        });

        return isValid;
    }

})(jQuery);
