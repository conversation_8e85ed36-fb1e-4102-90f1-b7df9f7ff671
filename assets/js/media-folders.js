/**
 * Media Folders JavaScript
 * Handles folder management in WordPress admin
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initMediaFolders();
    });

    /**
     * Initialize media folders functionality
     */
    function initMediaFolders() {
        addFolderManagementButtons();
        handleBulkActions();
        enhanceMediaLibrary();
    }

    /**
     * Add folder management buttons to media library
     */
    function addFolderManagementButtons() {
        // Add create folder button
        if ($('.media-toolbar-primary').length) {
            var createButton = $('<button type="button" class="button media-button button-primary button-large media-folder-create">' + 
                                '<span class="dashicons dashicons-plus-alt"></span> ' + mediaFolders.strings.createFolder + '</button>');
            $('.media-toolbar-primary').prepend(createButton);
        }

        // Add folder management to upload.php
        if ($('body.upload-php').length) {
            var folderActions = $('<div class="media-folder-actions">' +
                '<button type="button" class="button media-folder-create">' +
                '<span class="dashicons dashicons-plus-alt"></span> ' + mediaFolders.strings.createFolder +
                '</button>' +
                '</div>');
            $('.tablenav.top .alignleft.actions').after(folderActions);
        }

        // Handle create folder button click
        $(document).on('click', '.media-folder-create', function(e) {
            e.preventDefault();
            showCreateFolderDialog();
        });
    }

    /**
     * Show create folder dialog
     */
    function showCreateFolderDialog() {
        var dialog = $('<div class="media-folder-dialog">' +
            '<div class="media-folder-dialog-content">' +
                '<h3>' + mediaFolders.strings.createFolder + '</h3>' +
                '<p>' +
                    '<label for="folder-name">' + mediaFolders.strings.folderName + ':</label>' +
                    '<input type="text" id="folder-name" class="widefat" placeholder="' + mediaFolders.strings.folderName + '">' +
                '</p>' +
                '<p class="submit">' +
                    '<button type="button" class="button button-primary folder-create-submit">' + mediaFolders.strings.create + '</button>' +
                    '<button type="button" class="button folder-dialog-cancel">' + mediaFolders.strings.cancel + '</button>' +
                '</p>' +
            '</div>' +
            '<div class="media-folder-dialog-backdrop"></div>' +
        '</div>');

        $('body').append(dialog);
        dialog.find('#folder-name').focus();

        // Handle dialog actions
        dialog.find('.folder-create-submit').on('click', function() {
            createFolder(dialog.find('#folder-name').val(), dialog);
        });

        dialog.find('.folder-dialog-cancel, .media-folder-dialog-backdrop').on('click', function() {
            dialog.remove();
        });

        // Handle enter key
        dialog.find('#folder-name').on('keypress', function(e) {
            if (e.which === 13) {
                createFolder($(this).val(), dialog);
            }
        });
    }

    /**
     * Create new folder via AJAX
     */
    function createFolder(folderName, dialog) {
        if (!folderName.trim()) {
            alert(mediaFolders.strings.folderName + ' is required.');
            return;
        }

        var submitButton = dialog.find('.folder-create-submit');
        submitButton.prop('disabled', true).text('Creating...');

        $.ajax({
            url: mediaFolders.ajaxurl,
            type: 'POST',
            data: {
                action: 'create_media_folder',
                folder_name: folderName,
                nonce: mediaFolders.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    dialog.remove();
                    
                    // Refresh page to show new folder
                    if ($('body.upload-php').length) {
                        location.reload();
                    }
                    
                    // Update folder dropdown if exists
                    updateFolderDropdown(response.data);
                } else {
                    showNotice(response.data || mediaFolders.strings.error, 'error');
                    submitButton.prop('disabled', false).text(mediaFolders.strings.create);
                }
            },
            error: function() {
                showNotice(mediaFolders.strings.error, 'error');
                submitButton.prop('disabled', false).text(mediaFolders.strings.create);
            }
        });
    }

    /**
     * Update folder dropdown with new folder
     */
    function updateFolderDropdown(folderData) {
        var dropdown = $('#media-folder-filter');
        if (dropdown.length) {
            var option = $('<option value="' + folderData.slug + '">' + folderData.name + ' (0)</option>');
            dropdown.append(option);
        }

        // Update attachment edit dropdowns
        $('select[name*="[media_folder]"]').each(function() {
            var option = $('<option value="' + folderData.term_id + '">' + folderData.name + '</option>');
            $(this).append(option);
        });
    }

    /**
     * Handle bulk actions for moving files to folders
     */
    function handleBulkActions() {
        // Override bulk action handling
        $(document).on('click', '#doaction, #doaction2', function(e) {
            var action = $(this).siblings('select').val();
            
            if (action === 'move_to_folder') {
                e.preventDefault();
                showMoveToFolderDialog();
            }
        });
    }

    /**
     * Show move to folder dialog
     */
    function showMoveToFolderDialog() {
        var selectedItems = $('input[name="media[]"]:checked');
        
        if (selectedItems.length === 0) {
            alert('Please select files to move.');
            return;
        }

        // Get available folders
        $.ajax({
            url: mediaFolders.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_media_folders',
                nonce: mediaFolders.nonce
            },
            success: function(response) {
                if (response.success) {
                    showFolderSelectionDialog(selectedItems, response.data);
                }
            }
        });
    }

    /**
     * Show folder selection dialog
     */
    function showFolderSelectionDialog(selectedItems, folders) {
        var folderOptions = '<option value="0">' + mediaFolders.strings.selectFolder + '</option>';
        
        folders.forEach(function(folder) {
            folderOptions += '<option value="' + folder.term_id + '">' + folder.name + '</option>';
        });

        var dialog = $('<div class="media-folder-dialog">' +
            '<div class="media-folder-dialog-content">' +
                '<h3>' + mediaFolders.strings.moveToFolder + '</h3>' +
                '<p>Moving ' + selectedItems.length + ' file(s) to folder:</p>' +
                '<p>' +
                    '<select id="target-folder" class="widefat">' + folderOptions + '</select>' +
                '</p>' +
                '<p class="submit">' +
                    '<button type="button" class="button button-primary folder-move-submit">' + mediaFolders.strings.move + '</button>' +
                    '<button type="button" class="button folder-dialog-cancel">' + mediaFolders.strings.cancel + '</button>' +
                '</p>' +
            '</div>' +
            '<div class="media-folder-dialog-backdrop"></div>' +
        '</div>');

        $('body').append(dialog);

        // Handle dialog actions
        dialog.find('.folder-move-submit').on('click', function() {
            var folderId = dialog.find('#target-folder').val();
            if (folderId === '0') {
                alert('Please select a folder.');
                return;
            }
            moveFilesToFolder(selectedItems, folderId, dialog);
        });

        dialog.find('.folder-dialog-cancel, .media-folder-dialog-backdrop').on('click', function() {
            dialog.remove();
        });
    }

    /**
     * Move files to folder via AJAX
     */
    function moveFilesToFolder(selectedItems, folderId, dialog) {
        var attachmentIds = [];
        selectedItems.each(function() {
            attachmentIds.push($(this).val());
        });

        var submitButton = dialog.find('.folder-move-submit');
        submitButton.prop('disabled', true).text('Moving...');

        $.ajax({
            url: mediaFolders.ajaxurl,
            type: 'POST',
            data: {
                action: 'move_media_to_folder',
                attachment_ids: attachmentIds,
                folder_id: folderId,
                nonce: mediaFolders.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                    dialog.remove();
                    location.reload(); // Refresh to show changes
                } else {
                    showNotice(response.data || mediaFolders.strings.error, 'error');
                    submitButton.prop('disabled', false).text(mediaFolders.strings.move);
                }
            },
            error: function() {
                showNotice(mediaFolders.strings.error, 'error');
                submitButton.prop('disabled', false).text(mediaFolders.strings.move);
            }
        });
    }

    /**
     * Enhance media library with folder information
     */
    function enhanceMediaLibrary() {
        // Add folder info to media items in list view
        if ($('body.upload-php .wp-list-table').length) {
            addFolderColumnInfo();
        }

        // Add folder filter enhancement
        enhanceFolderFilter();
    }

    /**
     * Add folder information to media list
     */
    function addFolderColumnInfo() {
        // This would require server-side changes to add folder column
        // For now, we'll add folder info via JavaScript if needed
    }

    /**
     * Enhance folder filter dropdown
     */
    function enhanceFolderFilter() {
        var filterDropdown = $('#media-folder-filter');
        
        if (filterDropdown.length) {
            // Add clear filter button
            var clearButton = $('<button type="button" class="button clear-folder-filter" title="Clear filter">×</button>');
            filterDropdown.after(clearButton);

            clearButton.on('click', function() {
                filterDropdown.val('').trigger('change');
                $(this).closest('form').submit();
            });
        }
    }

    /**
     * Show admin notice
     */
    function showNotice(message, type) {
        var notice = $('<div class="notice notice-' + type + ' is-dismissible">' +
            '<p>' + message + '</p>' +
            '<button type="button" class="notice-dismiss">' +
                '<span class="screen-reader-text">Dismiss this notice.</span>' +
            '</button>' +
        '</div>');

        $('.wrap h1').after(notice);

        // Auto dismiss after 5 seconds
        setTimeout(function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);

        // Handle manual dismiss
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        });
    }

})(jQuery);
