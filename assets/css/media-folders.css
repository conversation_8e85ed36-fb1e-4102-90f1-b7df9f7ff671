/**
 * Media Folders CSS
 * Styling for media folder management interface
 */

/* ==========================================================================
   Media Folder Dialog
   ========================================================================== */

.media-folder-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-folder-dialog-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    cursor: pointer;
}

.media-folder-dialog-content {
    position: relative;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 20px;
    min-width: 400px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.media-folder-dialog-content h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    line-height: 1.4;
}

.media-folder-dialog-content p {
    margin: 0 0 15px 0;
}

.media-folder-dialog-content label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.media-folder-dialog-content input[type="text"],
.media-folder-dialog-content select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.media-folder-dialog-content input[type="text"]:focus,
.media-folder-dialog-content select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.media-folder-dialog-content .submit {
    text-align: right;
    margin: 20px 0 0 0;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.media-folder-dialog-content .button {
    margin-left: 10px;
}

/* ==========================================================================
   Media Folder Actions
   ========================================================================== */

.media-folder-actions {
    display: inline-block;
    margin-left: 10px;
}

.media-folder-actions .button {
    vertical-align: top;
}

.media-folder-create .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    vertical-align: text-top;
    margin-right: 5px;
}

/* Media Library Toolbar */
.media-toolbar-primary .media-folder-create {
    margin-right: 10px;
}

.media-toolbar-primary .media-folder-create .dashicons {
    margin-right: 3px;
}

/* ==========================================================================
   Folder Filter Enhancement
   ========================================================================== */

#media-folder-filter {
    min-width: 150px;
}

.clear-folder-filter {
    margin-left: 5px !important;
    padding: 2px 8px !important;
    font-size: 16px !important;
    line-height: 1 !important;
    height: auto !important;
    min-height: auto !important;
    border-radius: 50% !important;
    background: #dc3232 !important;
    color: white !important;
    border: none !important;
}

.clear-folder-filter:hover {
    background: #a00 !important;
}

/* ==========================================================================
   Media List Enhancements
   ========================================================================== */

.wp-list-table .column-media_folder {
    width: 15%;
}

.media-folder-badge {
    display: inline-block;
    background: #0073aa;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.media-folder-badge.no-folder {
    background: #999;
}

/* ==========================================================================
   Attachment Edit Screen
   ========================================================================== */

.compat-field-media_folder th {
    width: 20%;
    vertical-align: top;
    padding-top: 8px;
}

.compat-field-media_folder td {
    width: 80%;
}

.compat-field-media_folder select {
    width: 100%;
    max-width: 300px;
}

.compat-field-media_folder .help {
    font-style: italic;
    color: #666;
    margin: 5px 0 0 0;
    font-size: 12px;
}

/* ==========================================================================
   Bulk Actions Enhancement
   ========================================================================== */

.tablenav .actions select[name="action"],
.tablenav .actions select[name="action2"] {
    min-width: 120px;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media screen and (max-width: 782px) {
    .media-folder-dialog-content {
        min-width: 300px;
        padding: 15px;
    }
    
    .media-folder-actions {
        margin: 10px 0;
        display: block;
    }
    
    .media-toolbar-primary .media-folder-create {
        margin: 5px 0;
        display: block;
        width: 100%;
    }
    
    #media-folder-filter {
        min-width: auto;
        width: 100%;
        margin-bottom: 5px;
    }
    
    .clear-folder-filter {
        margin: 5px 0 0 0 !important;
        display: block;
        width: auto;
    }
}

@media screen and (max-width: 480px) {
    .media-folder-dialog-content {
        min-width: 280px;
        padding: 10px;
    }
    
    .media-folder-dialog-content .submit {
        text-align: center;
    }
    
    .media-folder-dialog-content .button {
        margin: 5px;
        display: block;
        width: calc(50% - 10px);
        display: inline-block;
    }
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.media-folder-loading {
    opacity: 0.6;
    pointer-events: none;
}

.media-folder-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: media-folder-spin 1s linear infinite;
}

@keyframes media-folder-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Notice Enhancements
   ========================================================================== */

.notice.media-folder-notice {
    margin: 15px 0;
}

.notice.media-folder-notice p {
    margin: 0.5em 0;
}

/* ==========================================================================
   Media Modal Enhancements (for media uploader)
   ========================================================================== */

.media-modal .media-folder-select {
    margin: 10px 0;
}

.media-modal .media-folder-select label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.media-modal .media-folder-select select {
    width: 100%;
    max-width: 300px;
}

/* ==========================================================================
   Folder Hierarchy Display
   ========================================================================== */

.media-folder-hierarchy {
    padding-left: 20px;
    position: relative;
}

.media-folder-hierarchy::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #ddd;
}

.media-folder-hierarchy .media-folder-item {
    position: relative;
    padding: 5px 0;
}

.media-folder-hierarchy .media-folder-item::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 15px;
    width: 12px;
    height: 1px;
    background: #ddd;
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .media-folder-dialog-backdrop {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .media-folder-dialog-content {
        background: #1d2327;
        color: #f0f0f1;
    }
    
    .media-folder-dialog-content input[type="text"],
    .media-folder-dialog-content select {
        background: #2c3338;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .media-folder-dialog-content input[type="text"]:focus,
    .media-folder-dialog-content select:focus {
        border-color: #00a0d2;
        box-shadow: 0 0 0 1px #00a0d2;
    }
    
    .media-folder-badge {
        background: #00a0d2;
    }
    
    .media-folder-badge.no-folder {
        background: #646970;
    }
}
