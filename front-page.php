<?php
/**
 * The front page template file
 *
 * @package Autodebruin
 */

get_header();
?>

<main id="primary" class="site-main front-page">

    <!-- Search Section -->
    <section class="search-section">
        <div class="container">
            <div class="search-widget">
                <h2><?php esc_html_e('ZOEK UW VOERTUIG', 'autodebruin'); ?></h2>
                <form class="occasion-search-form" method="get" action="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>">
                    <div class="search-fields">
                        <div class="search-field">
                            <label for="search-merk"><?php esc_html_e('Merk', 'autodebruin'); ?></label>
                            <select name="merk" id="search-merk">
                                <option value=""><?php esc_html_e('Alle merken', 'autodebruin'); ?></option>
                                <?php
                                $merken = get_terms(array(
                                    'taxonomy' => 'merk',
                                    'hide_empty' => true,
                                ));
                                foreach ($merken as $merk) {
                                    echo '<option value="' . esc_attr($merk->slug) . '">' . esc_html($merk->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="search-field">
                            <label for="search-brandstof"><?php esc_html_e('Brandstof', 'autodebruin'); ?></label>
                            <select name="brandstof" id="search-brandstof">
                                <option value=""><?php esc_html_e('Alle brandstoffen', 'autodebruin'); ?></option>
                                <?php
                                $brandstoffen = get_terms(array(
                                    'taxonomy' => 'brandstof',
                                    'hide_empty' => true,
                                ));
                                foreach ($brandstoffen as $brandstof) {
                                    echo '<option value="' . esc_attr($brandstof->slug) . '">' . esc_html($brandstof->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="search-field">
                            <label for="search-price"><?php esc_html_e('Max. Prijs', 'autodebruin'); ?></label>
                            <select name="max_price" id="search-price">
                                <option value=""><?php esc_html_e('Geen limiet', 'autodebruin'); ?></option>
                                <option value="10000">€ 10.000</option>
                                <option value="15000">€ 15.000</option>
                                <option value="20000">€ 20.000</option>
                                <option value="25000">€ 25.000</option>
                                <option value="30000">€ 30.000</option>
                                <option value="50000">€ 50.000</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <?php esc_html_e('Zoeken', 'autodebruin'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- USP Section -->
    <section class="usp-section">
        <div class="container">
            <div class="usp-grid">
                <div class="usp-item">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/usp-inruilprijs.png" alt="Gunstigste inruilprijs">
                    <h3><?php esc_html_e('De gunstigste inruilprijs', 'autodebruin'); ?></h3>
                    <p><?php esc_html_e('Wij bieden altijd een eerlijke en scherpe inruilprijs voor uw huidige voertuig.', 'autodebruin'); ?></p>
                </div>
                
                <div class="usp-item">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/usp-garantie.png" alt="Garantie service">
                    <h3><?php esc_html_e('Garantie service', 'autodebruin'); ?></h3>
                    <p><?php esc_html_e('Professionele service en garantie op al onze bedrijfswagens.', 'autodebruin'); ?></p>
                </div>
                
                <div class="usp-item">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/usp-mogelijkheden.png" alt="Financiële mogelijkheden">
                    <h3><?php esc_html_e('Financiële mogelijkheden', 'autodebruin'); ?></h3>
                    <p><?php esc_html_e('Flexibele financierings- en leasemogelijkheden op maat.', 'autodebruin'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Showcase Section -->
    <section class="showcase-section">
        <div class="container">
            <div class="showcase-images">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/showcase-01.jpg" alt="Bedrijfswagen showcase 1">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/showcase-02.jpg" alt="Bedrijfswagen showcase 2">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/showcase-03.jpg" alt="Bedrijfswagen showcase 3">
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about-section">
        <div class="container">
            <div class="row">
                <div class="col-6">
                    <h2><?php esc_html_e('De bedrijfswagen specialist', 'autodebruin'); ?></h2>
                    <ul class="about-features">
                        <li><?php esc_html_e('Meer dan 66 jaar automotive bedrijfswagen specialist', 'autodebruin'); ?></li>
                        <li><?php esc_html_e('Altijd persoonlijk contact en advies op maat', 'autodebruin'); ?></li>
                        <li><?php esc_html_e('Service garantie van een professioneel team', 'autodebruin'); ?></li>
                        <li><?php esc_html_e('Breed aanbod in bedrijfswagen', 'autodebruin'); ?></li>
                    </ul>
                    
                    <p><?php esc_html_e('Autodebruin is in 1959 opgericht door C. de Bruin. Al ruim 66 jaar lang leveren we uitstekende bedrijfswagens en personenauto\'s met service die u verwacht. Heeft u een vraag, wilt u langskomen of een afspraak maken voor een proefrit?', 'autodebruin'); ?></p>
                    
                    <p><?php esc_html_e('Vul dan het formulier in of maak gebruik van een van de contactmogelijkheden die u hieronder kunt vinden.', 'autodebruin'); ?></p>
                    
                    <div class="contact-info">
                        <p>
                            <strong><?php esc_html_e('Neem contact op:', 'autodebruin'); ?></strong><br>
                            <a href="tel:0180312484">0180 - 312484</a><br>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="contact-form-widget">
                        <?php echo do_shortcode('[contact-form-7 id="1" title="Homepage Contact Form"]'); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Occasions Section -->
    <section class="featured-occasions-section">
        <div class="container">
            <div class="section-header">
                <h2><?php esc_html_e('Uitgelicht aanbod', 'autodebruin'); ?></h2>
                <a href="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>" class="btn btn-primary">
                    <?php esc_html_e('VOLLEDIG AANBOD BEKIJKEN', 'autodebruin'); ?>
                </a>
            </div>
            
            <div class="occasions-grid">
                <?php
                $featured_occasions = new WP_Query(array(
                    'post_type' => 'occasion',
                    'posts_per_page' => 3,
                    'meta_query' => array(
                        array(
                            'key' => '_featured',
                            'value' => '1',
                            'compare' => '='
                        )
                    )
                ));
                
                if ($featured_occasions->have_posts()) :
                    while ($featured_occasions->have_posts()) : $featured_occasions->the_post();
                        get_template_part('template-parts/content', 'occasion-card');
                    endwhile;
                    wp_reset_postdata();
                else :
                    // Fallback: show latest occasions if no featured ones
                    $latest_occasions = new WP_Query(array(
                        'post_type' => 'occasion',
                        'posts_per_page' => 3,
                    ));
                    
                    if ($latest_occasions->have_posts()) :
                        while ($latest_occasions->have_posts()) : $latest_occasions->the_post();
                            get_template_part('template-parts/content', 'occasion-card');
                        endwhile;
                        wp_reset_postdata();
                    endif;
                endif;
                ?>
            </div>
        </div>
    </section>

    <!-- Financial Lease Section -->
    <section class="financial-lease-section">
        <div class="container">
            <div class="row">
                <div class="col-6">
                    <h2><?php esc_html_e('Maak kennis met onze voordelige financial lease', 'autodebruin'); ?></h2>
                    <h3><?php esc_html_e('Zakelijk uw bedrijfswagen leasen', 'autodebruin'); ?></h3>
                    
                    <p><?php esc_html_e('Bij Autodebruin.nl is het mogelijk om met behulp van financial lease (of zakelijk leasen) een bedrijfsauto aan te schaffen.', 'autodebruin'); ?></p>
                    
                    <p><?php esc_html_e('U bent direct eigenaar van de bedrijfsauto en uw onderneming behoudt haar liquiditeit. Kortom, financial lease is in vele opzichten erg voordelig', 'autodebruin'); ?></p>
                    
                    <h4><?php esc_html_e('Heeft u een vraag over onze lease mogelijkheden?', 'autodebruin'); ?></h4>
                    
                    <div class="contact-info">
                        <p>
                            <strong><?php esc_html_e('Neem contact op:', 'autodebruin'); ?></strong><br>
                            <a href="tel:0180312484">0180 - 312484</a><br>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="lease-info-box">
                        <h3><?php esc_html_e('Bedrijfswagen kopen?', 'autodebruin'); ?></h3>
                        <p><?php esc_html_e('Omdat geen enkel bedrijf hetzelfde is, doet AutodeBruin er alles aan om met u samen te werken om een oplossing te vinden die aan uw unieke behoeften voldoet.', 'autodebruin'); ?></p>
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('bedrijfswagen-kopen'))); ?>" class="btn btn-primary">
                            <?php esc_html_e('Bekijk merken', 'autodebruin'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <h2><?php esc_html_e('Wat onze klanten zeggen', 'autodebruin'); ?></h2>
            
            <div class="testimonials-grid">
                <div class="testimonial">
                    <p>"<?php esc_html_e('Werden vriendelijk geholpen en mochten eigenlijk meteen een proefrit maken.', 'autodebruin'); ?>"</p>
                </div>
                
                <div class="testimonial">
                    <p>"<?php esc_html_e('Ik kan niets anders zeggen dan dat we super geholpen zijn!', 'autodebruin'); ?>"</p>
                </div>
                
                <div class="testimonial">
                    <p>"<?php esc_html_e('We zijn erg tevreden met de auto en met de service van dit bedrijf.', 'autodebruin'); ?>"</p>
                </div>
                
                <div class="testimonial">
                    <p>"<?php esc_html_e('Peugeot 308 gekocht bij de Bruyn voor een scherpe prijs, over de inruilprijs waren we het ook snel eens.', 'autodebruin'); ?>"</p>
                </div>
            </div>
            
            <div class="testimonial-cta">
                <h3><?php esc_html_e('Ook zo tevreden?', 'autodebruin'); ?></h3>
                <a href="https://reviewthis.biz/d79a3151" target="_blank" rel="noopener" class="btn btn-primary">
                    <?php esc_html_e('Schrijf hier een beoordeling', 'autodebruin'); ?>
                </a>
            </div>
        </div>
    </section>

</main>

<?php
get_footer();
?>
