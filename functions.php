<?php
/**
 * Autodebruin Theme Functions
 *
 * @package Autodebruin
 * <AUTHOR> - Funnel Adviseur
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function autodebruin_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('responsive-embeds');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'autodebruin'),
        'footer' => __('Footer Menu', 'autodebruin'),
    ));

    // Add image sizes
    add_image_size('occasion-thumbnail', 400, 300, true);
    add_image_size('occasion-large', 800, 600, true);
    add_image_size('hero-image', 1200, 600, true);
}
add_action('after_setup_theme', 'autodebruin_setup');

/**
 * Enqueue Scripts and Styles
 */
function autodebruin_scripts() {
    // Enqueue styles
    wp_enqueue_style('autodebruin-style', get_stylesheet_uri(), array(), '1.0.0');
    wp_enqueue_style('autodebruin-main', get_template_directory_uri() . '/assets/css/main.css', array(), '1.0.0');

    // Enqueue scripts
    wp_enqueue_script('autodebruin-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);

    // Localize script for AJAX
    wp_localize_script('autodebruin-main', 'autodebruin_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('autodebruin_nonce')
    ));

    // Load comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'autodebruin_scripts');

/**
 * Register Widget Areas
 */
function autodebruin_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'autodebruin'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'autodebruin'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer 1', 'autodebruin'),
        'id'            => 'footer-1',
        'description'   => __('Footer widget area 1.', 'autodebruin'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer 2', 'autodebruin'),
        'id'            => 'footer-2',
        'description'   => __('Footer widget area 2.', 'autodebruin'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer 3', 'autodebruin'),
        'id'            => 'footer-3',
        'description'   => __('Footer widget area 3.', 'autodebruin'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'autodebruin_widgets_init');

/**
 * Register Custom Post Types
 */
function autodebruin_register_post_types() {
    // Register Occasion CPT
    register_post_type('occasion', array(
        'labels' => array(
            'name' => __('Occasions', 'autodebruin'),
            'singular_name' => __('Occasion', 'autodebruin'),
            'menu_name' => __('Occasions', 'autodebruin'),
            'add_new' => __('Add New Occasion', 'autodebruin'),
            'add_new_item' => __('Add New Occasion', 'autodebruin'),
            'edit_item' => __('Edit Occasion', 'autodebruin'),
            'new_item' => __('New Occasion', 'autodebruin'),
            'view_item' => __('View Occasion', 'autodebruin'),
            'search_items' => __('Search Occasions', 'autodebruin'),
            'not_found' => __('No occasions found', 'autodebruin'),
            'not_found_in_trash' => __('No occasions found in trash', 'autodebruin'),
        ),
        'public' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'occasions'),
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'menu_icon' => 'dashicons-car',
        'show_in_rest' => true,
    ));
}
add_action('init', 'autodebruin_register_post_types');

/**
 * Register Custom Taxonomies
 */
function autodebruin_register_taxonomies() {
    // Register Merk taxonomy
    register_taxonomy('merk', 'occasion', array(
        'labels' => array(
            'name' => __('Merken', 'autodebruin'),
            'singular_name' => __('Merk', 'autodebruin'),
            'menu_name' => __('Merken', 'autodebruin'),
            'add_new_item' => __('Add New Merk', 'autodebruin'),
            'edit_item' => __('Edit Merk', 'autodebruin'),
            'update_item' => __('Update Merk', 'autodebruin'),
            'view_item' => __('View Merk', 'autodebruin'),
            'search_items' => __('Search Merken', 'autodebruin'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'merk'),
        'show_in_rest' => true,
    ));

    // Register Brandstof taxonomy
    register_taxonomy('brandstof', 'occasion', array(
        'labels' => array(
            'name' => __('Brandstoffen', 'autodebruin'),
            'singular_name' => __('Brandstof', 'autodebruin'),
            'menu_name' => __('Brandstoffen', 'autodebruin'),
            'add_new_item' => __('Add New Brandstof', 'autodebruin'),
            'edit_item' => __('Edit Brandstof', 'autodebruin'),
            'update_item' => __('Update Brandstof', 'autodebruin'),
            'view_item' => __('View Brandstof', 'autodebruin'),
            'search_items' => __('Search Brandstoffen', 'autodebruin'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'brandstof'),
        'show_in_rest' => true,
    ));

    // Register Transmissie taxonomy
    register_taxonomy('transmissie', 'occasion', array(
        'labels' => array(
            'name' => __('Transmissies', 'autodebruin'),
            'singular_name' => __('Transmissie', 'autodebruin'),
            'menu_name' => __('Transmissies', 'autodebruin'),
            'add_new_item' => __('Add New Transmissie', 'autodebruin'),
            'edit_item' => __('Edit Transmissie', 'autodebruin'),
            'update_item' => __('Update Transmissie', 'autodebruin'),
            'view_item' => __('View Transmissie', 'autodebruin'),
            'search_items' => __('Search Transmissies', 'autodebruin'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'transmissie'),
        'show_in_rest' => true,
    ));
}
add_action('init', 'autodebruin_register_taxonomies');

/**
 * Add Custom Meta Boxes for Occasions
 */
function autodebruin_add_meta_boxes() {
    add_meta_box(
        'occasion_details',
        __('Occasion Details', 'autodebruin'),
        'autodebruin_occasion_details_callback',
        'occasion',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'autodebruin_add_meta_boxes');

/**
 * Meta Box Callback Function
 */
function autodebruin_occasion_details_callback($post) {
    wp_nonce_field('autodebruin_save_meta_box_data', 'autodebruin_meta_box_nonce');

    $price = get_post_meta($post->ID, '_occasion_price', true);
    $kilometers = get_post_meta($post->ID, '_occasion_kilometers', true);
    $year = get_post_meta($post->ID, '_occasion_year', true);
    $kenteken = get_post_meta($post->ID, '_occasion_kenteken', true);
    $laadvermogen = get_post_meta($post->ID, '_occasion_laadvermogen', true);
    $pk = get_post_meta($post->ID, '_occasion_pk', true);
    $topsnelheid = get_post_meta($post->ID, '_occasion_topsnelheid', true);
    $gewicht = get_post_meta($post->ID, '_occasion_gewicht', true);
    $kleur = get_post_meta($post->ID, '_occasion_kleur', true);
    $deuren = get_post_meta($post->ID, '_occasion_deuren', true);
    $energielabel = get_post_meta($post->ID, '_occasion_energielabel', true);

    echo '<table class="form-table">';
    echo '<tr><th><label for="occasion_price">Prijs (€)</label></th><td><input type="number" id="occasion_price" name="occasion_price" value="' . esc_attr($price) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_kilometers">Kilometers</label></th><td><input type="number" id="occasion_kilometers" name="occasion_kilometers" value="' . esc_attr($kilometers) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_year">Bouwjaar</label></th><td><input type="number" id="occasion_year" name="occasion_year" value="' . esc_attr($year) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_kenteken">Kenteken</label></th><td><input type="text" id="occasion_kenteken" name="occasion_kenteken" value="' . esc_attr($kenteken) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_laadvermogen">Laadvermogen (kg)</label></th><td><input type="number" id="occasion_laadvermogen" name="occasion_laadvermogen" value="' . esc_attr($laadvermogen) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_pk">Vermogen (PK)</label></th><td><input type="number" id="occasion_pk" name="occasion_pk" value="' . esc_attr($pk) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_topsnelheid">Topsnelheid (km/h)</label></th><td><input type="number" id="occasion_topsnelheid" name="occasion_topsnelheid" value="' . esc_attr($topsnelheid) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_gewicht">Gewicht (kg)</label></th><td><input type="number" id="occasion_gewicht" name="occasion_gewicht" value="' . esc_attr($gewicht) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_kleur">Kleur</label></th><td><input type="text" id="occasion_kleur" name="occasion_kleur" value="' . esc_attr($kleur) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_deuren">Aantal deuren</label></th><td><input type="number" id="occasion_deuren" name="occasion_deuren" value="' . esc_attr($deuren) . '" /></td></tr>';
    echo '<tr><th><label for="occasion_energielabel">Energielabel</label></th><td><input type="text" id="occasion_energielabel" name="occasion_energielabel" value="' . esc_attr($energielabel) . '" /></td></tr>';
    echo '</table>';
}

/**
 * Save Meta Box Data
 */
function autodebruin_save_meta_box_data($post_id) {
    if (!isset($_POST['autodebruin_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['autodebruin_meta_box_nonce'], 'autodebruin_save_meta_box_data')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (isset($_POST['post_type']) && 'occasion' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    $fields = array(
        'occasion_price', 'occasion_kilometers', 'occasion_year', 'occasion_kenteken',
        'occasion_laadvermogen', 'occasion_pk', 'occasion_topsnelheid', 'occasion_gewicht',
        'occasion_kleur', 'occasion_deuren', 'occasion_energielabel'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'autodebruin_save_meta_box_data');

/**
 * Custom Excerpt Length
 */
function autodebruin_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'autodebruin_excerpt_length', 999);

/**
 * Custom Excerpt More
 */
function autodebruin_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'autodebruin_excerpt_more');

/**
 * Helper functions for post meta
 */
function autodebruin_posted_on() {
    $time_string = '<time class="entry-date published updated" datetime="%1$s">%2$s</time>';
    if (get_the_time('U') !== get_the_modified_time('U')) {
        $time_string = '<time class="entry-date published" datetime="%1$s">%2$s</time><time class="updated" datetime="%3$s">%4$s</time>';
    }

    $time_string = sprintf(
        $time_string,
        esc_attr(get_the_date(DATE_W3C)),
        esc_html(get_the_date()),
        esc_attr(get_the_modified_date(DATE_W3C)),
        esc_html(get_the_modified_date())
    );

    $posted_on = sprintf(
        esc_html_x('Geplaatst op %s', 'post date', 'autodebruin'),
        '<a href="' . esc_url(get_permalink()) . '" rel="bookmark">' . $time_string . '</a>'
    );

    echo '<span class="posted-on">' . $posted_on . '</span>';
}

function autodebruin_posted_by() {
    $byline = sprintf(
        esc_html_x('door %s', 'post author', 'autodebruin'),
        '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
    );

    echo '<span class="byline"> ' . $byline . '</span>';
}

function autodebruin_entry_footer() {
    // Hide category and tag text for pages.
    if ('post' === get_post_type()) {
        $categories_list = get_the_category_list(esc_html__(', ', 'autodebruin'));
        if ($categories_list) {
            printf('<span class="cat-links">' . esc_html__('Geplaatst in %1$s', 'autodebruin') . '</span>', $categories_list);
        }

        $tags_list = get_the_tag_list('', esc_html_x(', ', 'list item separator', 'autodebruin'));
        if ($tags_list) {
            printf('<span class="tags-links">' . esc_html__('Getagd %1$s', 'autodebruin') . '</span>', $tags_list);
        }
    }

    if (!is_single() && !post_password_required() && (comments_open() || get_comments_number())) {
        echo '<span class="comments-link">';
        comments_popup_link(
            sprintf(
                wp_kses(
                    __('Reactie plaatsen<span class="screen-reader-text"> op %s</span>', 'autodebruin'),
                    array(
                        'span' => array(
                            'class' => array(),
                        ),
                    )
                ),
                wp_kses_post(get_the_title())
            )
        );
        echo '</span>';
    }

    edit_post_link(
        sprintf(
            wp_kses(
                __('Bewerken <span class="screen-reader-text">%s</span>', 'autodebruin'),
                array(
                    'span' => array(
                        'class' => array(),
                    ),
                )
            ),
            wp_kses_post(get_the_title())
        ),
        '<span class="edit-link">',
        '</span>'
    );
}

/**
 * Add Font Awesome
 */
function autodebruin_add_fontawesome() {
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
}
add_action('wp_enqueue_scripts', 'autodebruin_add_fontawesome');

/**
 * Add Google Fonts
 */
function autodebruin_add_google_fonts() {
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
}
add_action('wp_enqueue_scripts', 'autodebruin_add_google_fonts');

/**
 * Add AOS (Animate On Scroll) Library
 */
function autodebruin_add_aos_library() {
    wp_enqueue_style('aos-css', 'https://unpkg.com/aos@2.3.1/dist/aos.css', array(), '2.3.1');
    wp_enqueue_script('aos-js', 'https://unpkg.com/aos@2.3.1/dist/aos.js', array(), '2.3.1', true);
}
add_action('wp_enqueue_scripts', 'autodebruin_add_aos_library');

/**
 * Fallback menu for desktop
 */
function autodebruin_fallback_menu() {
    echo '<ul id="primary-menu" class="elementor-nav-menu">';
    echo '<li class="menu-item"><a href="' . esc_url(home_url('/')) . '" class="elementor-item">' . esc_html__('Home', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_post_type_archive_link('occasion')) . '" class="elementor-item">' . esc_html__('Occasions', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_permalink(get_page_by_path('service'))) . '" class="elementor-item">' . esc_html__('Service', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_permalink(get_page_by_path('financial-lease'))) . '" class="elementor-item">' . esc_html__('Financial Lease', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_permalink(get_page_by_path('contact'))) . '" class="elementor-item">' . esc_html__('Contact', 'autodebruin') . '</a></li>';
    echo '</ul>';
}

/**
 * Fallback menu for mobile
 */
function autodebruin_mobile_fallback_menu() {
    echo '<ul id="mobile-dropdown-menu" class="elementor-nav-menu">';
    echo '<li class="menu-item"><a href="' . esc_url(home_url('/')) . '" class="elementor-item">' . esc_html__('Home', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_post_type_archive_link('occasion')) . '" class="elementor-item">' . esc_html__('Occasions', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_post_type_archive_link('occasion')) . '?_merk=peugeot" class="elementor-item">' . esc_html__('Peugeot Bedrijfswagens', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_permalink(get_page_by_path('service'))) . '" class="elementor-item">' . esc_html__('Service', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_permalink(get_page_by_path('financial-lease'))) . '" class="elementor-item">' . esc_html__('Financial Lease', 'autodebruin') . '</a></li>';
    echo '<li class="menu-item"><a href="' . esc_url(get_permalink(get_page_by_path('contact'))) . '" class="elementor-item">' . esc_html__('Contact', 'autodebruin') . '</a></li>';
    echo '</ul>';
}

/**
 * Include additional functions
 */
require_once get_template_directory() . '/inc/template-functions.php';
require_once get_template_directory() . '/inc/customizer.php';
require_once get_template_directory() . '/inc/ajax-functions.php';
