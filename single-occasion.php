<?php
/**
 * The template for displaying single occasion posts
 *
 * @package Autodebruin
 */

get_header();
?>

<main id="primary" class="site-main single-occasion">
    <?php while (have_posts()) : the_post(); ?>
        
        <div class="container">
            
            <!-- Breadcrumb -->
            <nav class="breadcrumb">
                <a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Home', 'autodebruin'); ?></a>
                <span class="separator">/</span>
                <a href="<?php echo esc_url(get_post_type_archive_link('occasion')); ?>"><?php esc_html_e('Occasions', 'autodebruin'); ?></a>
                <span class="separator">/</span>
                <span class="current"><?php the_title(); ?></span>
            </nav>

            <!-- Occasion Header -->
            <header class="occasion-header">
                <h1 class="occasion-title"><?php the_title(); ?></h1>
                <div class="occasion-subtitle">
                    <?php echo esc_html(get_the_excerpt()); ?>
                </div>
                
                <div class="occasion-actions">
                    <button onclick="window.print()" class="btn btn-secondary">
                        <i class="fas fa-print"></i>
                        <?php esc_html_e('Print deze pagina', 'autodebruin'); ?>
                    </button>
                    <a href="#contact-form" class="btn btn-primary">
                        <?php esc_html_e('Vraag vrijblijvend een proefrit', 'autodebruin'); ?>
                    </a>
                </div>
            </header>

            <div class="occasion-content-wrapper">
                <div class="row">
                    
                    <!-- Left Column: Images and Details -->
                    <div class="col-8">
                        
                        <!-- Price Section -->
                        <div class="occasion-price-section">
                            <?php
                            $price = get_post_meta(get_the_ID(), '_occasion_price', true);
                            $lease_price = get_post_meta(get_the_ID(), '_occasion_lease_price', true);
                            ?>
                            <div class="price-display">
                                <h2 class="main-price">€ <?php echo number_format($price, 0, ',', '.'); ?>,-</h2>
                                <?php if ($lease_price) : ?>
                                    <p class="lease-price"><?php esc_html_e('Lease vanaf:', 'autodebruin'); ?> € <?php echo esc_html($lease_price); ?>,-</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Image Gallery -->
                        <div class="occasion-gallery">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="main-image">
                                    <?php the_post_thumbnail('occasion-large', array('class' => 'occasion-main-image')); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $gallery_images = get_post_meta(get_the_ID(), '_occasion_gallery', true);
                            if ($gallery_images) :
                                ?>
                                <div class="gallery-thumbnails">
                                    <?php foreach ($gallery_images as $image_id) : ?>
                                        <img src="<?php echo wp_get_attachment_image_url($image_id, 'occasion-thumbnail'); ?>" 
                                             alt="<?php echo get_post_meta($image_id, '_wp_attachment_image_alt', true); ?>"
                                             class="gallery-thumb">
                                    <?php endforeach; ?>
                                </div>
                                <?php
                            endif;
                            ?>
                        </div>

                        <!-- Overview Section -->
                        <section class="occasion-overview">
                            <h3><?php printf(esc_html__('Overzicht van deze %s', 'autodebruin'), get_the_title()); ?></h3>
                            <p class="guarantee-text">
                                <i class="fas fa-shield-alt"></i>
                                <?php esc_html_e('Ga veilig en vertrouwd op weg met via onze BOVAG garantie', 'autodebruin'); ?>
                            </p>
                            
                            <div class="specs-grid">
                                <?php
                                $specs = array(
                                    'gewicht' => array('icon' => 'weight', 'label' => 'Gewicht', 'unit' => 'kg'),
                                    'kilometers' => array('icon' => 'tachometer-alt', 'label' => 'Aantal KM', 'unit' => 'km'),
                                    'brandstof' => array('icon' => 'gas-pump', 'label' => 'Brandstof', 'unit' => ''),
                                    'year' => array('icon' => 'calendar', 'label' => 'Bouwjaar', 'unit' => ''),
                                    'transmissie' => array('icon' => 'cogs', 'label' => 'Transmissie', 'unit' => ''),
                                    'pk' => array('icon' => 'bolt', 'label' => 'Aantal PK', 'unit' => 'pk'),
                                    'laadvermogen' => array('icon' => 'weight-hanging', 'label' => 'Laadvermogen', 'unit' => 'kg'),
                                );
                                
                                foreach ($specs as $key => $spec) :
                                    $value = '';
                                    if ($key == 'brandstof' || $key == 'transmissie') {
                                        $terms = get_the_terms(get_the_ID(), $key);
                                        if ($terms && !is_wp_error($terms)) {
                                            $value = $terms[0]->name;
                                        }
                                    } else {
                                        $value = get_post_meta(get_the_ID(), '_occasion_' . $key, true);
                                    }
                                    
                                    if ($value) :
                                        ?>
                                        <div class="spec-item">
                                            <div class="spec-icon">
                                                <i class="fas fa-<?php echo esc_attr($spec['icon']); ?>"></i>
                                            </div>
                                            <div class="spec-content">
                                                <h4><?php echo esc_html($spec['label']); ?></h4>
                                                <p><?php echo esc_html($value . ' ' . $spec['unit']); ?></p>
                                            </div>
                                        </div>
                                        <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </section>

                        <!-- Testimonials Section -->
                        <section class="testimonials-section">
                            <h3><?php esc_html_e('Wat onze klanten zeggen', 'autodebruin'); ?></h3>
                            <p class="testimonials-intro"><?php esc_html_e('Lees hoe anderen onze service ervaren. Onze klanten delen hun eerlijke mening over de samenwerking, kwaliteit en resultaten.', 'autodebruin'); ?></p>
                            
                            <div class="testimonials-carousel">
                                <div class="testimonial">
                                    <h4><?php esc_html_e('Prettige koopervaring', 'autodebruin'); ?></h4>
                                    <p><?php esc_html_e('Geen opdringerige verkoop, maar een eerlijk advies. Helemaal tevreden met mijn nieuwe bedrijfswagen!', 'autodebruin'); ?></p>
                                </div>
                                
                                <div class="testimonial">
                                    <h4><?php esc_html_e('Goede service en nazorg', 'autodebruin'); ?></h4>
                                    <p><?php esc_html_e('Ook na de aankoop nog een paar vragen gesteld en snel geholpen. Echt top!', 'autodebruin'); ?></p>
                                </div>
                                
                                <div class="testimonial">
                                    <h4><?php esc_html_e('Makkelijk en snel geregeld', 'autodebruin'); ?></h4>
                                    <p><?php esc_html_e('Online contact, proefrit gepland en binnen een week was alles rond. Super efficiënt!', 'autodebruin'); ?></p>
                                </div>
                            </div>
                        </section>

                        <!-- Specifications Section -->
                        <section class="specifications-section">
                            <h3><?php esc_html_e('Specificaties', 'autodebruin'); ?></h3>
                            
                            <div class="specs-table">
                                <?php
                                $all_specs = array(
                                    'Carrosserie' => 'Bestelauto',
                                    'Kenteken' => get_post_meta(get_the_ID(), '_occasion_kenteken', true),
                                    'Bouwjaar' => get_post_meta(get_the_ID(), '_occasion_year', true),
                                    'Tellerstand' => get_post_meta(get_the_ID(), '_occasion_kilometers', true),
                                    'Laadvermogen' => get_post_meta(get_the_ID(), '_occasion_laadvermogen', true),
                                    'Merk' => get_the_terms(get_the_ID(), 'merk') ? get_the_terms(get_the_ID(), 'merk')[0]->name : '',
                                    'Topsnelheid' => get_post_meta(get_the_ID(), '_occasion_topsnelheid', true),
                                    'Brandstof' => get_the_terms(get_the_ID(), 'brandstof') ? get_the_terms(get_the_ID(), 'brandstof')[0]->name : '',
                                    'Aantal deuren' => get_post_meta(get_the_ID(), '_occasion_deuren', true),
                                    'Transmissie' => get_the_terms(get_the_ID(), 'transmissie') ? get_the_terms(get_the_ID(), 'transmissie')[0]->name : '',
                                    'Basiskleur' => get_post_meta(get_the_ID(), '_occasion_kleur', true),
                                    'Energielabel' => get_post_meta(get_the_ID(), '_occasion_energielabel', true),
                                );
                                
                                foreach ($all_specs as $label => $value) :
                                    if ($value) :
                                        ?>
                                        <div class="spec-row">
                                            <span class="spec-label"><?php echo esc_html($label); ?></span>
                                            <span class="spec-value"><?php echo esc_html($value); ?></span>
                                        </div>
                                        <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </section>

                        <!-- Description Section -->
                        <section class="description-section">
                            <h3><?php printf(esc_html__('Beschrijving over %s', 'autodebruin'), get_the_title()); ?></h3>
                            <div class="occasion-description">
                                <?php the_content(); ?>
                            </div>
                        </section>

                        <!-- Features Section -->
                        <?php
                        $features = get_post_meta(get_the_ID(), '_occasion_features', true);
                        if ($features) :
                            ?>
                            <section class="features-section">
                                <h3><?php esc_html_e('Uitrusting', 'autodebruin'); ?></h3>
                                
                                <div class="features-tabs">
                                    <div class="tab-buttons">
                                        <button class="tab-button active" data-tab="comfort"><?php esc_html_e('Comfort en interieur', 'autodebruin'); ?></button>
                                        <button class="tab-button" data-tab="exterior"><?php esc_html_e('Exterieur', 'autodebruin'); ?></button>
                                        <button class="tab-button" data-tab="other"><?php esc_html_e('Overige functies', 'autodebruin'); ?></button>
                                    </div>
                                    
                                    <div class="tab-content">
                                        <div id="comfort" class="tab-pane active">
                                            <ul class="features-list">
                                                <?php foreach ($features['comfort'] as $feature) : ?>
                                                    <li><?php echo esc_html($feature); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                        
                                        <div id="exterior" class="tab-pane">
                                            <ul class="features-list">
                                                <?php foreach ($features['exterior'] as $feature) : ?>
                                                    <li><?php echo esc_html($feature); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                        
                                        <div id="other" class="tab-pane">
                                            <ul class="features-list">
                                                <?php foreach ($features['other'] as $feature) : ?>
                                                    <li><?php echo esc_html($feature); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </section>
                            <?php
                        endif;
                        ?>

                        <!-- Similar Occasions -->
                        <section class="similar-occasions">
                            <h3><?php esc_html_e('Vergelijkbare occasions', 'autodebruin'); ?></h3>
                            
                            <div class="similar-occasions-grid">
                                <?php
                                $current_merk = get_the_terms(get_the_ID(), 'merk');
                                $merk_slug = $current_merk ? $current_merk[0]->slug : '';
                                
                                $similar_occasions = new WP_Query(array(
                                    'post_type' => 'occasion',
                                    'posts_per_page' => 4,
                                    'post__not_in' => array(get_the_ID()),
                                    'tax_query' => array(
                                        array(
                                            'taxonomy' => 'merk',
                                            'field' => 'slug',
                                            'terms' => $merk_slug,
                                        ),
                                    ),
                                ));
                                
                                if ($similar_occasions->have_posts()) :
                                    while ($similar_occasions->have_posts()) : $similar_occasions->the_post();
                                        get_template_part('template-parts/content', 'occasion-card-small');
                                    endwhile;
                                    wp_reset_postdata();
                                endif;
                                ?>
                            </div>
                        </section>

                    </div>

                    <!-- Right Column: Contact and Info -->
                    <div class="col-4">
                        <div class="occasion-sidebar">
                            
                            <!-- Price and Contact -->
                            <div class="price-contact-widget">
                                <div class="price-display">
                                    <h3><?php esc_html_e('Verkoop Prijs', 'autodebruin'); ?></h3>
                                    <div class="main-price">€ <?php echo number_format($price, 0, ',', '.'); ?>,-</div>
                                    <?php if ($lease_price) : ?>
                                        <div class="lease-price"><?php esc_html_e('Lease voor maar:', 'autodebruin'); ?> € <?php echo esc_html($lease_price); ?>,-</div>
                                    <?php endif; ?>
                                </div>
                                
                                <a href="#contact-form" class="btn btn-primary btn-block">
                                    <?php esc_html_e('Vraag vrijblijvend een proefrit', 'autodebruin'); ?>
                                </a>
                                
                                <div class="contact-info">
                                    <p><?php esc_html_e('Neem contact op', 'autodebruin'); ?></p>
                                    <ul>
                                        <li><a href="tel:0180312484">0180 - 312484</a></li>
                                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Lease Calculator -->
                            <div class="lease-calculator-widget">
                                <h3><?php esc_html_e('Wat kost deze auto maandelijks?', 'autodebruin'); ?></h3>
                                
                                <form id="lease-calculator" class="lease-calculator">
                                    <div class="calc-field">
                                        <label for="lease-amount"><?php esc_html_e('Leasebedrag', 'autodebruin'); ?></label>
                                        <input type="number" id="lease-amount" value="<?php echo esc_attr($price); ?>" readonly>
                                        <small><?php esc_html_e('Dit is de totale aankoopprijs van de auto.', 'autodebruin'); ?></small>
                                    </div>
                                    
                                    <div class="calc-field">
                                        <label for="down-payment"><?php esc_html_e('Aanbetaling', 'autodebruin'); ?></label>
                                        <input type="number" id="down-payment" value="0">
                                        <small><?php esc_html_e('Dit is het bedrag dat je aanbetaalt, wat het te financieren bedrag verlaagt.', 'autodebruin'); ?></small>
                                    </div>
                                    
                                    <div class="calc-field">
                                        <label for="final-payment"><?php esc_html_e('Slottermijn', 'autodebruin'); ?></label>
                                        <input type="number" id="final-payment" value="0">
                                        <small><?php esc_html_e('Dit is het bedrag dat je aan het einde van de looptijd nog betaalt.', 'autodebruin'); ?></small>
                                    </div>
                                    
                                    <div class="calc-field">
                                        <label for="lease-term"><?php esc_html_e('Looptijd', 'autodebruin'); ?></label>
                                        <select id="lease-term">
                                            <option value="12">12 maanden</option>
                                            <option value="24">24 maanden</option>
                                            <option value="36" selected>36 maanden</option>
                                            <option value="48">48 maanden</option>
                                            <option value="60">60 maanden</option>
                                        </select>
                                        <small><?php esc_html_e('Dit is de duur van de leaseperiode in maanden.', 'autodebruin'); ?></small>
                                    </div>
                                    
                                    <div class="calc-field">
                                        <label>
                                            <input type="checkbox" id="trade-in">
                                            <?php esc_html_e('Je huidige auto inruilen?', 'autodebruin'); ?>
                                        </label>
                                    </div>
                                    
                                    <div class="calc-result">
                                        <strong><?php esc_html_e('Maandbedrag', 'autodebruin'); ?> <span id="monthly-amount">€ 0</span> p/m</strong>
                                    </div>
                                    
                                    <button type="button" class="btn btn-secondary btn-block" id="request-quote">
                                        <?php esc_html_e('Gratis offerte aanvragen', 'autodebruin'); ?>
                                    </button>
                                </form>
                            </div>

                            <!-- Contact Widget -->
                            <div class="contact-widget">
                                <h3><?php esc_html_e('Neem contact op', 'autodebruin'); ?></h3>
                                
                                <div class="company-badge">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/embleem-autodebruin-66-jaar.png" 
                                         alt="66 jaar ervaring">
                                </div>
                                
                                <div class="contact-methods">
                                    <div class="contact-method">
                                        <strong><?php esc_html_e('Bel meteen:', 'autodebruin'); ?></strong>
                                        <a href="tel:0180312484">0180 - 312484</a>
                                    </div>
                                    
                                    <div class="contact-method">
                                        <strong><?php esc_html_e('Adres:', 'autodebruin'); ?></strong>
                                        <p>Eerste Tochtweg 2, Nieuwerkerk ad IJssel</p>
                                    </div>
                                    
                                    <div class="contact-actions">
                                        <a href="https://wa.me/31180312484" class="btn btn-whatsapp" target="_blank">
                                            <i class="fab fa-whatsapp"></i>
                                            <?php esc_html_e('Stuur een WhatsApp', 'autodebruin'); ?>
                                        </a>
                                        <a href="#contact-form" class="btn btn-secondary">
                                            <?php esc_html_e('Vul het formulier in', 'autodebruin'); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>

            <!-- Contact Form Section -->
            <section id="contact-form" class="contact-form-section">
                <div class="container">
                    <h2><?php esc_html_e('Neem gerust contact op!', 'autodebruin'); ?></h2>
                    
                    <div class="row">
                        <div class="col-8">
                            <p><?php esc_html_e('Heb je vragen over een auto, financieringsmogelijkheden of wil je persoonlijk advies van onze specialisten? We staan voor je klaar en helpen je graag verder.', 'autodebruin'); ?></p>
                            
                            <?php echo do_shortcode('[contact-form-7 id="2" title="Occasion Contact Form"]'); ?>
                        </div>
                        
                        <div class="col-4">
                            <div class="contact-image">
                                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/contact-person.jpg" 
                                     alt="Contact persoon">
                            </div>
                            
                            <div class="other-contact-methods">
                                <h4><?php esc_html_e('Andere contact mogelijkheden', 'autodebruin'); ?></h4>
                                
                                <div class="contact-method">
                                    <h5><?php esc_html_e('Bel - Meteen antwoord', 'autodebruin'); ?></h5>
                                    <a href="tel:0180312484">0180 – 312484</a>
                                </div>
                                
                                <div class="contact-method">
                                    <h5><?php esc_html_e('Email je vraag', 'autodebruin'); ?></h5>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </div>
                                
                                <div class="contact-method">
                                    <h5><?php esc_html_e('Bezoek de showroom', 'autodebruin'); ?></h5>
                                    <p>
                                        Eerste Tochtweg 2<br>
                                        2913 LP Nieuwerkerk ad IJssel
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

        </div>

    <?php endwhile; ?>
</main>

<!-- WhatsApp Float Button -->
<div class="whatsapp-float-occasion">
    <a href="https://wa.me/31644040156?text=<?php echo urlencode('Hallo, ik wil graag een proefrit aanvragen voor: Merk: ' . (get_the_terms(get_the_ID(), 'merk') ? get_the_terms(get_the_ID(), 'merk')[0]->name : '') . ' Model: ' . get_the_title() . ' Kenteken: ' . get_post_meta(get_the_ID(), '_occasion_kenteken', true)); ?>" 
       target="_blank" rel="noopener">
        <i class="fab fa-whatsapp"></i>
        <span><?php esc_html_e('WhatsApp proefrit aanvragen', 'autodebruin'); ?></span>
    </a>
    <div class="viewers-count">
        <?php esc_html_e('Vrijblijvend, makkelijk en snel', 'autodebruin'); ?>
        <span class="count">12 <?php esc_html_e('nu aan het kijken', 'autodebruin'); ?></span>
    </div>
</div>

<?php
get_footer();
?>
