<?php
/**
 * Autodebruin Theme Customizer
 *
 * @package Autodebruin
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function autodebruin_customize_register($wp_customize) {
    $wp_customize->get_setting('blogname')->transport         = 'postMessage';
    $wp_customize->get_setting('blogdescription')->transport  = 'postMessage';
    $wp_customize->get_setting('header_textcolor')->transport = 'postMessage';

    if (isset($wp_customize->selective_refresh)) {
        $wp_customize->selective_refresh->add_partial(
            'blogname',
            array(
                'selector'        => '.site-title a',
                'render_callback' => 'autodebruin_customize_partial_blogname',
            )
        );
        $wp_customize->selective_refresh->add_partial(
            'blogdescription',
            array(
                'selector'        => '.site-description',
                'render_callback' => 'autodebruin_customize_partial_blogdescription',
            )
        );
    }

    // Company Information Section
    $wp_customize->add_section('autodebruin_company_info', array(
        'title'    => __('Bedrijfsinformatie', 'autodebruin'),
        'priority' => 30,
    ));

    // Phone Number
    $wp_customize->add_setting('autodebruin_phone', array(
        'default'           => '0180-312484',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_phone', array(
        'label'    => __('Telefoonnummer', 'autodebruin'),
        'section'  => 'autodebruin_company_info',
        'type'     => 'text',
    ));

    // Email Address
    $wp_customize->add_setting('autodebruin_email', array(
        'default'           => '<EMAIL>',
        'sanitize_callback' => 'sanitize_email',
    ));

    $wp_customize->add_control('autodebruin_email', array(
        'label'    => __('E-mailadres', 'autodebruin'),
        'section'  => 'autodebruin_company_info',
        'type'     => 'email',
    ));

    // Address
    $wp_customize->add_setting('autodebruin_address', array(
        'default'           => 'Eerste Tochtweg 2, 2913 LP Nieuwerkerk ad IJssel',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));

    $wp_customize->add_control('autodebruin_address', array(
        'label'    => __('Adres', 'autodebruin'),
        'section'  => 'autodebruin_company_info',
        'type'     => 'textarea',
    ));

    // WhatsApp Number
    $wp_customize->add_setting('autodebruin_whatsapp', array(
        'default'           => '31180312484',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_whatsapp', array(
        'label'       => __('WhatsApp nummer', 'autodebruin'),
        'description' => __('Inclusief landcode (bijv. 31180312484)', 'autodebruin'),
        'section'     => 'autodebruin_company_info',
        'type'        => 'text',
    ));

    // Social Media Section
    $wp_customize->add_section('autodebruin_social_media', array(
        'title'    => __('Social Media', 'autodebruin'),
        'priority' => 35,
    ));

    // Facebook URL
    $wp_customize->add_setting('autodebruin_facebook', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('autodebruin_facebook', array(
        'label'    => __('Facebook URL', 'autodebruin'),
        'section'  => 'autodebruin_social_media',
        'type'     => 'url',
    ));

    // Twitter URL
    $wp_customize->add_setting('autodebruin_twitter', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('autodebruin_twitter', array(
        'label'    => __('Twitter URL', 'autodebruin'),
        'section'  => 'autodebruin_social_media',
        'type'     => 'url',
    ));

    // YouTube URL
    $wp_customize->add_setting('autodebruin_youtube', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('autodebruin_youtube', array(
        'label'    => __('YouTube URL', 'autodebruin'),
        'section'  => 'autodebruin_social_media',
        'type'     => 'url',
    ));

    // LinkedIn URL
    $wp_customize->add_setting('autodebruin_linkedin', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('autodebruin_linkedin', array(
        'label'    => __('LinkedIn URL', 'autodebruin'),
        'section'  => 'autodebruin_social_media',
        'type'     => 'url',
    ));

    // Homepage Settings Section
    $wp_customize->add_section('autodebruin_homepage', array(
        'title'    => __('Homepage Instellingen', 'autodebruin'),
        'priority' => 40,
    ));

    // Hero Title
    $wp_customize->add_setting('autodebruin_hero_title', array(
        'default'           => 'Sinds 1960 uw bedrijfswagen specialist',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_hero_title', array(
        'label'    => __('Hero Titel', 'autodebruin'),
        'section'  => 'autodebruin_homepage',
        'type'     => 'text',
    ));

    // Hero Subtitle
    $wp_customize->add_setting('autodebruin_hero_subtitle', array(
        'default'           => 'B2B service - Groot aanbod - Beste prijs',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_hero_subtitle', array(
        'label'    => __('Hero Ondertitel', 'autodebruin'),
        'section'  => 'autodebruin_homepage',
        'type'     => 'text',
    ));

    // Hero Background Image
    $wp_customize->add_setting('autodebruin_hero_bg', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'autodebruin_hero_bg', array(
        'label'    => __('Hero Achtergrondafbeelding', 'autodebruin'),
        'section'  => 'autodebruin_homepage',
        'settings' => 'autodebruin_hero_bg',
    )));

    // Show Featured Occasions
    $wp_customize->add_setting('autodebruin_show_featured', array(
        'default'           => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));

    $wp_customize->add_control('autodebruin_show_featured', array(
        'label'    => __('Toon uitgelichte occasions op homepage', 'autodebruin'),
        'section'  => 'autodebruin_homepage',
        'type'     => 'checkbox',
    ));

    // Number of Featured Occasions
    $wp_customize->add_setting('autodebruin_featured_count', array(
        'default'           => 3,
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('autodebruin_featured_count', array(
        'label'       => __('Aantal uitgelichte occasions', 'autodebruin'),
        'description' => __('Hoeveel occasions tonen op de homepage', 'autodebruin'),
        'section'     => 'autodebruin_homepage',
        'type'        => 'number',
        'input_attrs' => array(
            'min' => 1,
            'max' => 6,
        ),
    ));

    // Colors Section
    $wp_customize->add_section('autodebruin_colors', array(
        'title'    => __('Kleuren', 'autodebruin'),
        'priority' => 45,
    ));

    // Primary Color
    $wp_customize->add_setting('autodebruin_primary_color', array(
        'default'           => '#264F7F',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'autodebruin_primary_color', array(
        'label'    => __('Primaire kleur', 'autodebruin'),
        'section'  => 'autodebruin_colors',
        'settings' => 'autodebruin_primary_color',
    )));

    // Secondary Color
    $wp_customize->add_setting('autodebruin_secondary_color', array(
        'default'           => '#333333',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'autodebruin_secondary_color', array(
        'label'    => __('Secundaire kleur', 'autodebruin'),
        'section'  => 'autodebruin_colors',
        'settings' => 'autodebruin_secondary_color',
    )));

    // Footer Settings Section
    $wp_customize->add_section('autodebruin_footer', array(
        'title'    => __('Footer Instellingen', 'autodebruin'),
        'priority' => 50,
    ));

    // Footer Copyright Text
    $wp_customize->add_setting('autodebruin_footer_copyright', array(
        'default'           => 'Alle rechten voorbehouden.',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_footer_copyright', array(
        'label'    => __('Copyright tekst', 'autodebruin'),
        'section'  => 'autodebruin_footer',
        'type'     => 'text',
    ));

    // Show Powered By
    $wp_customize->add_setting('autodebruin_show_powered_by', array(
        'default'           => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));

    $wp_customize->add_control('autodebruin_show_powered_by', array(
        'label'    => __('Toon "Powered by Funnel Adviseur"', 'autodebruin'),
        'section'  => 'autodebruin_footer',
        'type'     => 'checkbox',
    ));

    // Opening Hours Section
    $wp_customize->add_section('autodebruin_opening_hours', array(
        'title'    => __('Openingstijden', 'autodebruin'),
        'priority' => 55,
    ));

    // Monday - Friday
    $wp_customize->add_setting('autodebruin_hours_weekdays', array(
        'default'           => '08:00 - 18:00',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_hours_weekdays', array(
        'label'    => __('Maandag - Vrijdag', 'autodebruin'),
        'section'  => 'autodebruin_opening_hours',
        'type'     => 'text',
    ));

    // Saturday
    $wp_customize->add_setting('autodebruin_hours_saturday', array(
        'default'           => '09:00 - 17:00',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_hours_saturday', array(
        'label'    => __('Zaterdag', 'autodebruin'),
        'section'  => 'autodebruin_opening_hours',
        'type'     => 'text',
    ));

    // Sunday
    $wp_customize->add_setting('autodebruin_hours_sunday', array(
        'default'           => 'Gesloten',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autodebruin_hours_sunday', array(
        'label'    => __('Zondag', 'autodebruin'),
        'section'  => 'autodebruin_opening_hours',
        'type'     => 'text',
    ));
}
add_action('customize_register', 'autodebruin_customize_register');

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function autodebruin_customize_partial_blogname() {
    bloginfo('name');
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function autodebruin_customize_partial_blogdescription() {
    bloginfo('description');
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function autodebruin_customize_preview_js() {
    wp_enqueue_script('autodebruin-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array('customize-preview'), '1.0.0', true);
}
add_action('customize_preview_init', 'autodebruin_customize_preview_js');

/**
 * Output custom CSS based on customizer settings
 */
function autodebruin_customizer_css() {
    $primary_color = get_theme_mod('autodebruin_primary_color', '#264F7F');
    $secondary_color = get_theme_mod('autodebruin_secondary_color', '#333333');
    $hero_bg = get_theme_mod('autodebruin_hero_bg', '');

    ?>
    <style type="text/css">
        :root {
            --primary-color: <?php echo esc_html($primary_color); ?>;
            --secondary-color: <?php echo esc_html($secondary_color); ?>;
        }

        .btn-primary,
        .main-navigation a:hover,
        .main-navigation .current-menu-item a {
            background-color: <?php echo esc_html($primary_color); ?>;
            border-color: <?php echo esc_html($primary_color); ?>;
        }

        .btn-primary:hover {
            background-color: <?php echo esc_html(autodebruin_darken_color($primary_color, 20)); ?>;
            border-color: <?php echo esc_html(autodebruin_darken_color($primary_color, 20)); ?>;
        }

        .occasion-price,
        .footer-section h3,
        a:hover {
            color: <?php echo esc_html($primary_color); ?>;
        }

        <?php if ($hero_bg) : ?>
        .hero-section {
            background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('<?php echo esc_url($hero_bg); ?>');
            background-size: cover;
            background-position: center;
        }
        <?php endif; ?>
    </style>
    <?php
}
add_action('wp_head', 'autodebruin_customizer_css');

/**
 * Helper function to darken a color
 */
function autodebruin_darken_color($color, $percent) {
    $color = str_replace('#', '', $color);
    $r = hexdec(substr($color, 0, 2));
    $g = hexdec(substr($color, 2, 2));
    $b = hexdec(substr($color, 4, 2));

    $r = max(0, min(255, $r - ($r * $percent / 100)));
    $g = max(0, min(255, $g - ($g * $percent / 100)));
    $b = max(0, min(255, $b - ($b * $percent / 100)));

    return sprintf('#%02x%02x%02x', $r, $g, $b);
}
