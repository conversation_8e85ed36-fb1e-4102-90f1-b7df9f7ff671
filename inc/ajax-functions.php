<?php
/**
 * AJAX Functions for Autodebruin Theme
 *
 * @package Autodebruin
 */

/**
 * AJAX handler for filtering occasions
 */
function autodebruin_filter_occasions_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autodebruin_nonce')) {
        wp_die('Security check failed');
    }
    
    // Get filter parameters
    $filters = array();
    
    if (isset($_POST['merk']) && !empty($_POST['merk'])) {
        $filters['merk'] = sanitize_text_field($_POST['merk']);
    }
    
    if (isset($_POST['brandstof']) && !empty($_POST['brandstof'])) {
        $filters['brandstof'] = sanitize_text_field($_POST['brandstof']);
    }
    
    if (isset($_POST['transmissie']) && !empty($_POST['transmissie'])) {
        $filters['transmissie'] = sanitize_text_field($_POST['transmissie']);
    }
    
    if (isset($_POST['min_price']) && !empty($_POST['min_price'])) {
        $filters['min_price'] = intval($_POST['min_price']);
    }
    
    if (isset($_POST['max_price']) && !empty($_POST['max_price'])) {
        $filters['max_price'] = intval($_POST['max_price']);
    }
    
    if (isset($_POST['min_year']) && !empty($_POST['min_year'])) {
        $filters['min_year'] = intval($_POST['min_year']);
    }
    
    if (isset($_POST['max_year']) && !empty($_POST['max_year'])) {
        $filters['max_year'] = intval($_POST['max_year']);
    }
    
    if (isset($_POST['kleur']) && !empty($_POST['kleur'])) {
        $filters['kleur'] = sanitize_text_field($_POST['kleur']);
    }
    
    if (isset($_POST['orderby']) && !empty($_POST['orderby'])) {
        $filters['orderby'] = sanitize_text_field($_POST['orderby']);
    }
    
    $paged = isset($_POST['paged']) ? intval($_POST['paged']) : 1;
    
    // Build query arguments
    $args = array(
        'post_type' => 'occasion',
        'posts_per_page' => 12,
        'paged' => $paged,
        'post_status' => 'publish'
    );
    
    // Meta query for price, year, etc.
    $meta_query = array('relation' => 'AND');
    
    if (isset($filters['min_price'])) {
        $meta_query[] = array(
            'key' => '_occasion_price',
            'value' => $filters['min_price'],
            'compare' => '>=',
            'type' => 'NUMERIC'
        );
    }
    
    if (isset($filters['max_price'])) {
        $meta_query[] = array(
            'key' => '_occasion_price',
            'value' => $filters['max_price'],
            'compare' => '<=',
            'type' => 'NUMERIC'
        );
    }
    
    if (isset($filters['min_year'])) {
        $meta_query[] = array(
            'key' => '_occasion_year',
            'value' => $filters['min_year'],
            'compare' => '>=',
            'type' => 'NUMERIC'
        );
    }
    
    if (isset($filters['max_year'])) {
        $meta_query[] = array(
            'key' => '_occasion_year',
            'value' => $filters['max_year'],
            'compare' => '<=',
            'type' => 'NUMERIC'
        );
    }
    
    if (isset($filters['kleur'])) {
        $meta_query[] = array(
            'key' => '_occasion_kleur',
            'value' => $filters['kleur'],
            'compare' => 'LIKE'
        );
    }
    
    if (count($meta_query) > 1) {
        $args['meta_query'] = $meta_query;
    }
    
    // Tax query for taxonomies
    $tax_query = array('relation' => 'AND');
    
    if (isset($filters['merk'])) {
        $tax_query[] = array(
            'taxonomy' => 'merk',
            'field' => 'slug',
            'terms' => $filters['merk']
        );
    }
    
    if (isset($filters['brandstof'])) {
        $tax_query[] = array(
            'taxonomy' => 'brandstof',
            'field' => 'slug',
            'terms' => $filters['brandstof']
        );
    }
    
    if (isset($filters['transmissie'])) {
        $tax_query[] = array(
            'taxonomy' => 'transmissie',
            'field' => 'slug',
            'terms' => $filters['transmissie']
        );
    }
    
    if (count($tax_query) > 1) {
        $args['tax_query'] = $tax_query;
    }
    
    // Sorting
    if (isset($filters['orderby'])) {
        switch ($filters['orderby']) {
            case 'price_low':
                $args['meta_key'] = '_occasion_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            case 'price_high':
                $args['meta_key'] = '_occasion_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'year_new':
                $args['meta_key'] = '_occasion_year';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'year_old':
                $args['meta_key'] = '_occasion_year';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            case 'km_low':
                $args['meta_key'] = '_occasion_kilometers';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            default:
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
        }
    }
    
    // Execute query
    $query = new WP_Query($args);
    
    $response = array(
        'success' => true,
        'data' => array(
            'occasions' => array(),
            'total' => $query->found_posts,
            'max_pages' => $query->max_num_pages,
            'current_page' => $paged
        )
    );
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            
            $occasion_data = autodebruin_get_occasion_card_data(get_the_ID());
            $response['data']['occasions'][] = $occasion_data;
        }
        wp_reset_postdata();
    }
    
    wp_send_json($response);
}
add_action('wp_ajax_filter_occasions', 'autodebruin_filter_occasions_ajax');
add_action('wp_ajax_nopriv_filter_occasions', 'autodebruin_filter_occasions_ajax');

/**
 * AJAX handler for lease calculator
 */
function autodebruin_calculate_lease_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autodebruin_nonce')) {
        wp_die('Security check failed');
    }
    
    $vehicle_price = floatval($_POST['vehicle_price']);
    $down_payment = floatval($_POST['down_payment']);
    $lease_term = intval($_POST['lease_term']);
    $residual_value = floatval($_POST['residual_value']);
    $interest_rate = 0.05; // 5% default interest rate
    
    // Calculate financed amount
    $financed_amount = $vehicle_price - $down_payment - $residual_value;
    
    // Calculate monthly payment (simple calculation)
    $monthly_interest = $interest_rate / 12;
    $monthly_payment = ($financed_amount * $monthly_interest * pow(1 + $monthly_interest, $lease_term)) / 
                      (pow(1 + $monthly_interest, $lease_term) - 1);
    
    $response = array(
        'success' => true,
        'monthly_payment' => round($monthly_payment, 2)
    );
    
    wp_send_json($response);
}
add_action('wp_ajax_calculate_lease', 'autodebruin_calculate_lease_ajax');
add_action('wp_ajax_nopriv_calculate_lease', 'autodebruin_calculate_lease_ajax');

/**
 * AJAX handler for contact form submission
 */
function autodebruin_submit_contact_form_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autodebruin_nonce')) {
        wp_die('Security check failed');
    }
    
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    $occasion_id = isset($_POST['occasion_id']) ? intval($_POST['occasion_id']) : 0;
    
    // Validate required fields
    if (empty($name) || empty($email) || empty($message)) {
        wp_send_json_error('Vul alle verplichte velden in.');
        return;
    }
    
    if (!is_email($email)) {
        wp_send_json_error('Voer een geldig e-mailadres in.');
        return;
    }
    
    // Prepare email
    $to = get_theme_mod('autodebruin_email', '<EMAIL>');
    $email_subject = 'Contactformulier: ' . $subject;
    
    $email_message = "Nieuwe bericht via contactformulier:\n\n";
    $email_message .= "Naam: " . $name . "\n";
    $email_message .= "E-mail: " . $email . "\n";
    $email_message .= "Telefoon: " . $phone . "\n";
    $email_message .= "Onderwerp: " . $subject . "\n\n";
    $email_message .= "Bericht:\n" . $message . "\n\n";
    
    if ($occasion_id) {
        $occasion_title = get_the_title($occasion_id);
        $occasion_url = get_permalink($occasion_id);
        $email_message .= "Betreft occasion: " . $occasion_title . "\n";
        $email_message .= "URL: " . $occasion_url . "\n";
    }
    
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $name . ' <' . $email . '>',
        'Reply-To: ' . $email
    );
    
    // Send email
    $sent = wp_mail($to, $email_subject, $email_message, $headers);
    
    if ($sent) {
        wp_send_json_success('Uw bericht is verzonden. Wij nemen zo snel mogelijk contact met u op.');
    } else {
        wp_send_json_error('Er is een fout opgetreden bij het verzenden van uw bericht. Probeer het opnieuw of neem telefonisch contact op.');
    }
}
add_action('wp_ajax_submit_contact_form', 'autodebruin_submit_contact_form_ajax');
add_action('wp_ajax_nopriv_submit_contact_form', 'autodebruin_submit_contact_form_ajax');

/**
 * AJAX handler for getting occasion details
 */
function autodebruin_get_occasion_details_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autodebruin_nonce')) {
        wp_die('Security check failed');
    }
    
    $occasion_id = intval($_POST['occasion_id']);
    
    if (!$occasion_id || get_post_type($occasion_id) !== 'occasion') {
        wp_send_json_error('Ongeldige occasion ID.');
        return;
    }
    
    $occasion_data = autodebruin_get_occasion_card_data($occasion_id);
    
    wp_send_json_success($occasion_data);
}
add_action('wp_ajax_get_occasion_details', 'autodebruin_get_occasion_details_ajax');
add_action('wp_ajax_nopriv_get_occasion_details', 'autodebruin_get_occasion_details_ajax');

/**
 * AJAX handler for search suggestions
 */
function autodebruin_search_suggestions_ajax() {
    $search_term = sanitize_text_field($_POST['term']);
    
    if (strlen($search_term) < 2) {
        wp_send_json_error('Search term too short');
        return;
    }
    
    $suggestions = array();
    
    // Search in occasions
    $occasions = get_posts(array(
        'post_type' => 'occasion',
        'posts_per_page' => 5,
        's' => $search_term,
        'post_status' => 'publish'
    ));
    
    foreach ($occasions as $occasion) {
        $suggestions[] = array(
            'label' => $occasion->post_title,
            'value' => $occasion->post_title,
            'url' => get_permalink($occasion->ID),
            'type' => 'occasion'
        );
    }
    
    // Search in taxonomies
    $merken = get_terms(array(
        'taxonomy' => 'merk',
        'name__like' => $search_term,
        'hide_empty' => true,
        'number' => 3
    ));
    
    foreach ($merken as $merk) {
        $suggestions[] = array(
            'label' => $merk->name . ' (' . $merk->count . ' occasions)',
            'value' => $merk->name,
            'url' => get_term_link($merk),
            'type' => 'merk'
        );
    }
    
    wp_send_json_success($suggestions);
}
add_action('wp_ajax_search_suggestions', 'autodebruin_search_suggestions_ajax');
add_action('wp_ajax_nopriv_search_suggestions', 'autodebruin_search_suggestions_ajax');
