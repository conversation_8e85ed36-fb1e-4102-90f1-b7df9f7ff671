<?php
/**
 * Media Folders Functionality
 * Organizes media files into folders based on categories and custom post types
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize Media Folders
 */
function autodebruin_init_media_folders() {
    // Create custom taxonomy for media folders
    autodebruin_register_media_folder_taxonomy();

    // Add hooks for automatic folder assignment
    add_action('add_attachment', 'autodebruin_auto_assign_media_folder');
    add_action('edit_attachment', 'autodebruin_auto_assign_media_folder');

    // Add admin UI enhancements
    add_action('admin_enqueue_scripts', 'autodebruin_media_folders_admin_scripts');
    add_filter('attachment_fields_to_edit', 'autodebruin_add_folder_field_to_media', 10, 2);
    add_filter('attachment_fields_to_save', 'autodebruin_save_folder_field_to_media', 10, 2);

    // Add media library filters
    add_action('restrict_manage_posts', 'autodebruin_add_media_folder_filter');
    add_filter('parse_query', 'autodebruin_filter_media_by_folder');

    // Add folder management to media library
    add_action('wp_ajax_create_media_folder', 'autodebruin_ajax_create_media_folder');
    add_action('wp_ajax_delete_media_folder', 'autodebruin_ajax_delete_media_folder');
    add_action('wp_ajax_move_media_to_folder', 'autodebruin_ajax_move_media_to_folder');
}
add_action('init', 'autodebruin_init_media_folders');

/**
 * Register Media Folder Taxonomy
 */
function autodebruin_register_media_folder_taxonomy() {
    $labels = array(
        'name'              => __('Media Folders', 'autodebruin'),
        'singular_name'     => __('Media Folder', 'autodebruin'),
        'search_items'      => __('Search Folders', 'autodebruin'),
        'all_items'         => __('All Folders', 'autodebruin'),
        'parent_item'       => __('Parent Folder', 'autodebruin'),
        'parent_item_colon' => __('Parent Folder:', 'autodebruin'),
        'edit_item'         => __('Edit Folder', 'autodebruin'),
        'update_item'       => __('Update Folder', 'autodebruin'),
        'add_new_item'      => __('Add New Folder', 'autodebruin'),
        'new_item_name'     => __('New Folder Name', 'autodebruin'),
        'menu_name'         => __('Media Folders', 'autodebruin'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => false,
        'show_tagcloud'     => false,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'media-folder'),
        'capabilities'      => array(
            'manage_terms' => 'manage_options',
            'edit_terms'   => 'manage_options',
            'delete_terms' => 'manage_options',
            'assign_terms' => 'upload_files',
        ),
    );

    register_taxonomy('media_folder', 'attachment', $args);
}

/**
 * Automatically assign media to folders based on context
 */
function autodebruin_auto_assign_media_folder($attachment_id) {
    // Get the post this attachment is being uploaded to
    $parent_post_id = wp_get_post_parent_id($attachment_id);

    if ($parent_post_id) {
        $post_type = get_post_type($parent_post_id);

        // Create or get folder for this post type
        $folder_name = autodebruin_get_folder_name_for_post_type($post_type);
        $folder_term = autodebruin_get_or_create_media_folder($folder_name);

        if ($folder_term) {
            wp_set_object_terms($attachment_id, $folder_term->term_id, 'media_folder');
        }
    } else {
        // Check if we're in admin and can determine context
        $context = autodebruin_determine_upload_context();
        if ($context) {
            $folder_term = autodebruin_get_or_create_media_folder($context);
            if ($folder_term) {
                wp_set_object_terms($attachment_id, $folder_term->term_id, 'media_folder');
            }
        }
    }
}

/**
 * Get folder name for post type
 */
function autodebruin_get_folder_name_for_post_type($post_type) {
    $post_type_object = get_post_type_object($post_type);

    if ($post_type_object) {
        return $post_type_object->labels->name;
    }

    // Fallback mapping for common post types
    $folder_mapping = array(
        'post'      => 'Blog Posts',
        'page'      => 'Pages',
        'occasion'  => 'Occasions',
        'product'   => 'Products',
        'service'   => 'Services',
    );

    return isset($folder_mapping[$post_type]) ? $folder_mapping[$post_type] : ucfirst($post_type);
}

/**
 * Determine upload context from various sources
 */
function autodebruin_determine_upload_context() {
    // Check referer for context clues
    $referer = wp_get_referer();

    if ($referer) {
        // Check if uploading from a specific post type edit screen
        if (strpos($referer, 'post_type=occasion') !== false) {
            return 'Occasions';
        }
        if (strpos($referer, 'post_type=product') !== false) {
            return 'Products';
        }
        if (strpos($referer, 'post.php') !== false || strpos($referer, 'post-new.php') !== false) {
            return 'Blog Posts';
        }
        if (strpos($referer, 'customize.php') !== false) {
            return 'Theme Customization';
        }
    }

    // Check for AJAX context
    if (defined('DOING_AJAX') && DOING_AJAX) {
        $action = isset($_POST['action']) ? $_POST['action'] : '';
        if ($action === 'upload-attachment') {
            // Try to determine from post data
            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            if ($post_id) {
                $post_type = get_post_type($post_id);
                return autodebruin_get_folder_name_for_post_type($post_type);
            }
        }
    }

    return null;
}

/**
 * Get or create media folder term
 */
function autodebruin_get_or_create_media_folder($folder_name) {
    // Check if folder already exists
    $existing_term = get_term_by('name', $folder_name, 'media_folder');

    if ($existing_term) {
        return $existing_term;
    }

    // Create new folder
    $result = wp_insert_term($folder_name, 'media_folder');

    if (is_wp_error($result)) {
        return false;
    }

    return get_term($result['term_id'], 'media_folder');
}

/**
 * Add folder field to media edit screen
 */
function autodebruin_add_folder_field_to_media($form_fields, $post) {
    $folders = get_terms(array(
        'taxonomy' => 'media_folder',
        'hide_empty' => false,
    ));

    $current_folder = wp_get_object_terms($post->ID, 'media_folder');
    $current_folder_id = !empty($current_folder) ? $current_folder[0]->term_id : '';

    $options = '<option value="">' . __('No Folder', 'autodebruin') . '</option>';
    foreach ($folders as $folder) {
        $selected = ($current_folder_id == $folder->term_id) ? 'selected="selected"' : '';
        $options .= '<option value="' . $folder->term_id . '" ' . $selected . '>' . esc_html($folder->name) . '</option>';
    }

    $form_fields['media_folder'] = array(
        'label' => __('Media Folder', 'autodebruin'),
        'input' => 'html',
        'html'  => '<select name="attachments[' . $post->ID . '][media_folder]" id="attachments-' . $post->ID . '-media_folder">' . $options . '</select>
                   <p class="help">' . __('Organize this media file into a folder for better management.', 'autodebruin') . '</p>'
    );

    return $form_fields;
}

/**
 * Save folder field from media edit screen
 */
function autodebruin_save_folder_field_to_media($post, $attachment) {
    if (isset($attachment['media_folder'])) {
        $folder_id = intval($attachment['media_folder']);

        if ($folder_id > 0) {
            wp_set_object_terms($post['ID'], $folder_id, 'media_folder');
        } else {
            wp_delete_object_term_relationships($post['ID'], 'media_folder');
        }
    }

    return $post;
}

/**
 * Add folder filter to media library
 */
function autodebruin_add_media_folder_filter() {
    global $pagenow;

    if ($pagenow === 'upload.php') {
        $folders = get_terms(array(
            'taxonomy' => 'media_folder',
            'hide_empty' => false,
        ));

        if (!empty($folders)) {
            $current_folder = isset($_GET['media_folder']) ? $_GET['media_folder'] : '';

            echo '<select name="media_folder" id="media-folder-filter">';
            echo '<option value="">' . __('All Folders', 'autodebruin') . '</option>';

            foreach ($folders as $folder) {
                $selected = ($current_folder == $folder->slug) ? 'selected="selected"' : '';
                echo '<option value="' . $folder->slug . '" ' . $selected . '>' . esc_html($folder->name) . ' (' . $folder->count . ')</option>';
            }

            echo '</select>';
        }
    }
}

/**
 * Filter media by folder
 */
function autodebruin_filter_media_by_folder($query) {
    global $pagenow;

    if ($pagenow === 'upload.php' && isset($_GET['media_folder']) && !empty($_GET['media_folder'])) {
        $query->set('tax_query', array(
            array(
                'taxonomy' => 'media_folder',
                'field'    => 'slug',
                'terms'    => $_GET['media_folder'],
            )
        ));
    }
}

/**
 * Enqueue admin scripts for media folders
 */
function autodebruin_media_folders_admin_scripts($hook) {
    if ($hook === 'upload.php' || $hook === 'post.php' || $hook === 'post-new.php') {
        wp_enqueue_script(
            'autodebruin-media-folders',
            get_template_directory_uri() . '/assets/js/media-folders.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_localize_script('autodebruin-media-folders', 'mediaFolders', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce'   => wp_create_nonce('media_folders_nonce'),
            'strings' => array(
                'createFolder'    => __('Create New Folder', 'autodebruin'),
                'folderName'      => __('Folder Name', 'autodebruin'),
                'create'          => __('Create', 'autodebruin'),
                'cancel'          => __('Cancel', 'autodebruin'),
                'deleteFolder'    => __('Delete Folder', 'autodebruin'),
                'confirmDelete'   => __('Are you sure you want to delete this folder? Media files will not be deleted.', 'autodebruin'),
                'moveToFolder'    => __('Move to Folder', 'autodebruin'),
                'selectFolder'    => __('Select Folder', 'autodebruin'),
                'move'            => __('Move', 'autodebruin'),
                'success'         => __('Operation completed successfully', 'autodebruin'),
                'error'           => __('An error occurred', 'autodebruin'),
            )
        ));

        wp_enqueue_style(
            'autodebruin-media-folders',
            get_template_directory_uri() . '/assets/css/media-folders.css',
            array(),
            '1.0.0'
        );
    }
}

/**
 * AJAX: Create new media folder
 */
function autodebruin_ajax_create_media_folder() {
    check_ajax_referer('media_folders_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have permission to create folders.', 'autodebruin'));
    }

    $folder_name = sanitize_text_field($_POST['folder_name']);

    if (empty($folder_name)) {
        wp_send_json_error(__('Folder name is required.', 'autodebruin'));
    }

    $result = wp_insert_term($folder_name, 'media_folder');

    if (is_wp_error($result)) {
        wp_send_json_error($result->get_error_message());
    }

    $term = get_term($result['term_id'], 'media_folder');

    wp_send_json_success(array(
        'term_id' => $term->term_id,
        'name'    => $term->name,
        'slug'    => $term->slug,
        'message' => sprintf(__('Folder "%s" created successfully.', 'autodebruin'), $term->name)
    ));
}

/**
 * AJAX: Delete media folder
 */
function autodebruin_ajax_delete_media_folder() {
    check_ajax_referer('media_folders_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have permission to delete folders.', 'autodebruin'));
    }

    $term_id = intval($_POST['term_id']);

    if (!$term_id) {
        wp_send_json_error(__('Invalid folder ID.', 'autodebruin'));
    }

    $result = wp_delete_term($term_id, 'media_folder');

    if (is_wp_error($result)) {
        wp_send_json_error($result->get_error_message());
    }

    wp_send_json_success(__('Folder deleted successfully.', 'autodebruin'));
}

/**
 * AJAX: Move media to folder
 */
function autodebruin_ajax_move_media_to_folder() {
    check_ajax_referer('media_folders_nonce', 'nonce');

    if (!current_user_can('upload_files')) {
        wp_die(__('You do not have permission to move media files.', 'autodebruin'));
    }

    $attachment_ids = array_map('intval', $_POST['attachment_ids']);
    $folder_id = intval($_POST['folder_id']);

    if (empty($attachment_ids)) {
        wp_send_json_error(__('No media files selected.', 'autodebruin'));
    }

    $moved_count = 0;

    foreach ($attachment_ids as $attachment_id) {
        if ($folder_id > 0) {
            $result = wp_set_object_terms($attachment_id, $folder_id, 'media_folder');
        } else {
            $result = wp_delete_object_term_relationships($attachment_id, 'media_folder');
        }

        if (!is_wp_error($result)) {
            $moved_count++;
        }
    }

    if ($moved_count > 0) {
        $folder_name = $folder_id > 0 ? get_term($folder_id, 'media_folder')->name : __('No Folder', 'autodebruin');
        wp_send_json_success(sprintf(
            _n(
                '%d file moved to "%s".',
                '%d files moved to "%s".',
                $moved_count,
                'autodebruin'
            ),
            $moved_count,
            $folder_name
        ));
    } else {
        wp_send_json_error(__('No files were moved.', 'autodebruin'));
    }
}

/**
 * Add media folder management to admin menu
 */
function autodebruin_add_media_folder_admin_menu() {
    add_submenu_page(
        'upload.php',
        __('Media Folders', 'autodebruin'),
        __('Folders', 'autodebruin'),
        'manage_options',
        'edit-tags.php?taxonomy=media_folder&post_type=attachment'
    );
}
add_action('admin_menu', 'autodebruin_add_media_folder_admin_menu');

/**
 * Add bulk actions to media library
 */
function autodebruin_add_media_bulk_actions($bulk_actions) {
    $folders = get_terms(array(
        'taxonomy' => 'media_folder',
        'hide_empty' => false,
    ));

    if (!empty($folders)) {
        $bulk_actions['move_to_folder'] = __('Move to Folder', 'autodebruin');
    }

    return $bulk_actions;
}
add_filter('bulk_actions-upload', 'autodebruin_add_media_bulk_actions');

/**
 * Handle bulk actions for media library
 */
function autodebruin_handle_media_bulk_actions($redirect_to, $doaction, $post_ids) {
    if ($doaction === 'move_to_folder') {
        // This will be handled by JavaScript
        return $redirect_to;
    }

    return $redirect_to;
}
add_filter('handle_bulk_actions-upload', 'autodebruin_handle_media_bulk_actions', 10, 3);

/**
 * Create default folders on theme activation
 */
function autodebruin_create_default_media_folders() {
    $default_folders = array(
        'Occasions',
        'Blog Posts',
        'Pages',
        'Theme Assets',
        'User Uploads',
        'Products',
        'Services'
    );

    foreach ($default_folders as $folder_name) {
        autodebruin_get_or_create_media_folder($folder_name);
    }
}

/**
 * Initialize default folders on theme setup
 */
function autodebruin_setup_media_folders() {
    // Only create default folders if none exist
    $existing_folders = get_terms(array(
        'taxonomy' => 'media_folder',
        'hide_empty' => false,
        'count' => true
    ));

    if (empty($existing_folders)) {
        autodebruin_create_default_media_folders();
    }
}
add_action('after_switch_theme', 'autodebruin_setup_media_folders');

/**
 * AJAX: Get media folders for dropdown
 */
function autodebruin_ajax_get_media_folders() {
    check_ajax_referer('media_folders_nonce', 'nonce');

    $folders = get_terms(array(
        'taxonomy' => 'media_folder',
        'hide_empty' => false,
    ));

    if (is_wp_error($folders)) {
        wp_send_json_error($folders->get_error_message());
    }

    wp_send_json_success($folders);
}
add_action('wp_ajax_get_media_folders', 'autodebruin_ajax_get_media_folders');
