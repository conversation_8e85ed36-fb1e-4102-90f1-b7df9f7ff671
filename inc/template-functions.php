<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package Autodebruin
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
function autodebruin_body_classes($classes) {
    // Adds a class of hfeed to non-singular pages.
    if (!is_singular()) {
        $classes[] = 'hfeed';
    }

    // Adds a class of no-sidebar when there is no sidebar present.
    if (!is_active_sidebar('sidebar-1')) {
        $classes[] = 'no-sidebar';
    }

    return $classes;
}
add_filter('body_class', 'autodebruin_body_classes');

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function autodebruin_pingback_header() {
    if (is_singular() && pings_open()) {
        printf('<link rel="pingback" href="%s">', esc_url(get_bloginfo('pingback_url')));
    }
}
add_action('wp_head', 'autodebruin_pingback_header');

/**
 * Custom search form
 */
function autodebruin_search_form($form) {
    $form = '<form role="search" method="get" class="search-form" action="' . esc_url(home_url('/')) . '">
        <div class="search-input-group">
            <input type="search" class="search-field" placeholder="' . esc_attr_x('Zoeken...', 'placeholder', 'autodebruin') . '" value="' . get_search_query() . '" name="s" />
            <button type="submit" class="search-submit">
                <i class="fas fa-search"></i>
                <span class="sr-only">' . _x('Zoeken', 'submit button', 'autodebruin') . '</span>
            </button>
        </div>
    </form>';

    return $form;
}
add_filter('get_search_form', 'autodebruin_search_form');

/**
 * Filter occasions based on URL parameters
 */
function autodebruin_filter_occasions($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_post_type_archive('occasion') || is_tax(array('merk', 'brandstof', 'transmissie'))) {
            
            // Meta query array
            $meta_query = array('relation' => 'AND');
            
            // Price filters
            if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
                $meta_query[] = array(
                    'key' => '_occasion_price',
                    'value' => intval($_GET['min_price']),
                    'compare' => '>=',
                    'type' => 'NUMERIC'
                );
            }
            
            if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
                $meta_query[] = array(
                    'key' => '_occasion_price',
                    'value' => intval($_GET['max_price']),
                    'compare' => '<=',
                    'type' => 'NUMERIC'
                );
            }
            
            // Year filters
            if (isset($_GET['min_year']) && !empty($_GET['min_year'])) {
                $meta_query[] = array(
                    'key' => '_occasion_year',
                    'value' => intval($_GET['min_year']),
                    'compare' => '>=',
                    'type' => 'NUMERIC'
                );
            }
            
            if (isset($_GET['max_year']) && !empty($_GET['max_year'])) {
                $meta_query[] = array(
                    'key' => '_occasion_year',
                    'value' => intval($_GET['max_year']),
                    'compare' => '<=',
                    'type' => 'NUMERIC'
                );
            }
            
            // Kilometers filter
            if (isset($_GET['max_km']) && !empty($_GET['max_km'])) {
                $meta_query[] = array(
                    'key' => '_occasion_kilometers',
                    'value' => intval($_GET['max_km']),
                    'compare' => '<=',
                    'type' => 'NUMERIC'
                );
            }
            
            // Laadvermogen filter
            if (isset($_GET['min_laadvermogen']) && !empty($_GET['min_laadvermogen'])) {
                $meta_query[] = array(
                    'key' => '_occasion_laadvermogen',
                    'value' => intval($_GET['min_laadvermogen']),
                    'compare' => '>=',
                    'type' => 'NUMERIC'
                );
            }
            
            // Color filter
            if (isset($_GET['kleur']) && !empty($_GET['kleur'])) {
                $meta_query[] = array(
                    'key' => '_occasion_kleur',
                    'value' => sanitize_text_field($_GET['kleur']),
                    'compare' => 'LIKE'
                );
            }
            
            if (count($meta_query) > 1) {
                $query->set('meta_query', $meta_query);
            }
            
            // Tax query for taxonomies
            $tax_query = array('relation' => 'AND');
            
            if (isset($_GET['merk']) && !empty($_GET['merk'])) {
                $tax_query[] = array(
                    'taxonomy' => 'merk',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['merk'])
                );
            }
            
            if (isset($_GET['brandstof']) && !empty($_GET['brandstof'])) {
                $tax_query[] = array(
                    'taxonomy' => 'brandstof',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['brandstof'])
                );
            }
            
            if (isset($_GET['transmissie']) && !empty($_GET['transmissie'])) {
                $tax_query[] = array(
                    'taxonomy' => 'transmissie',
                    'field' => 'slug',
                    'terms' => sanitize_text_field($_GET['transmissie'])
                );
            }
            
            if (count($tax_query) > 1) {
                $query->set('tax_query', $tax_query);
            }
            
            // Sorting
            if (isset($_GET['orderby']) && !empty($_GET['orderby'])) {
                switch ($_GET['orderby']) {
                    case 'price_low':
                        $query->set('meta_key', '_occasion_price');
                        $query->set('orderby', 'meta_value_num');
                        $query->set('order', 'ASC');
                        break;
                    case 'price_high':
                        $query->set('meta_key', '_occasion_price');
                        $query->set('orderby', 'meta_value_num');
                        $query->set('order', 'DESC');
                        break;
                    case 'year_new':
                        $query->set('meta_key', '_occasion_year');
                        $query->set('orderby', 'meta_value_num');
                        $query->set('order', 'DESC');
                        break;
                    case 'year_old':
                        $query->set('meta_key', '_occasion_year');
                        $query->set('orderby', 'meta_value_num');
                        $query->set('order', 'ASC');
                        break;
                    case 'km_low':
                        $query->set('meta_key', '_occasion_kilometers');
                        $query->set('orderby', 'meta_value_num');
                        $query->set('order', 'ASC');
                        break;
                    default:
                        $query->set('orderby', 'date');
                        $query->set('order', 'DESC');
                        break;
                }
            }
        }
    }
}
add_action('pre_get_posts', 'autodebruin_filter_occasions');

/**
 * Custom pagination for occasions
 */
function autodebruin_occasion_pagination() {
    global $wp_query;
    
    $big = 999999999; // need an unlikely integer
    
    $pagination = paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'prev_text' => '&laquo;',
        'next_text' => '&raquo;',
        'type' => 'array',
        'end_size' => 3,
        'mid_size' => 3
    ));
    
    if ($pagination) {
        echo '<nav class="occasions-pagination"><ul class="pagination">';
        foreach ($pagination as $page) {
            echo '<li>' . $page . '</li>';
        }
        echo '</ul></nav>';
    }
}

/**
 * Get occasion specifications
 */
function autodebruin_get_occasion_specs($post_id) {
    $specs = array();
    
    $specs['price'] = get_post_meta($post_id, '_occasion_price', true);
    $specs['kilometers'] = get_post_meta($post_id, '_occasion_kilometers', true);
    $specs['year'] = get_post_meta($post_id, '_occasion_year', true);
    $specs['kenteken'] = get_post_meta($post_id, '_occasion_kenteken', true);
    $specs['laadvermogen'] = get_post_meta($post_id, '_occasion_laadvermogen', true);
    $specs['pk'] = get_post_meta($post_id, '_occasion_pk', true);
    $specs['topsnelheid'] = get_post_meta($post_id, '_occasion_topsnelheid', true);
    $specs['gewicht'] = get_post_meta($post_id, '_occasion_gewicht', true);
    $specs['kleur'] = get_post_meta($post_id, '_occasion_kleur', true);
    $specs['deuren'] = get_post_meta($post_id, '_occasion_deuren', true);
    $specs['energielabel'] = get_post_meta($post_id, '_occasion_energielabel', true);
    
    // Get taxonomy terms
    $merk_terms = get_the_terms($post_id, 'merk');
    $specs['merk'] = $merk_terms && !is_wp_error($merk_terms) ? $merk_terms[0]->name : '';
    
    $brandstof_terms = get_the_terms($post_id, 'brandstof');
    $specs['brandstof'] = $brandstof_terms && !is_wp_error($brandstof_terms) ? $brandstof_terms[0]->name : '';
    
    $transmissie_terms = get_the_terms($post_id, 'transmissie');
    $specs['transmissie'] = $transmissie_terms && !is_wp_error($transmissie_terms) ? $transmissie_terms[0]->name : '';
    
    return $specs;
}

/**
 * Format price for display
 */
function autodebruin_format_price($price) {
    if (empty($price)) {
        return '';
    }
    
    return '€ ' . number_format($price, 0, ',', '.') . ',-';
}

/**
 * Format kilometers for display
 */
function autodebruin_format_kilometers($kilometers) {
    if (empty($kilometers)) {
        return '';
    }
    
    return number_format($kilometers, 0, ',', '.') . ' km';
}

/**
 * Get occasion card data
 */
function autodebruin_get_occasion_card_data($post_id) {
    $data = array();
    
    $data['id'] = $post_id;
    $data['title'] = get_the_title($post_id);
    $data['permalink'] = get_permalink($post_id);
    $data['excerpt'] = get_the_excerpt($post_id);
    $data['thumbnail'] = get_the_post_thumbnail($post_id, 'occasion-thumbnail');
    $data['specs'] = autodebruin_get_occasion_specs($post_id);
    $data['featured'] = get_post_meta($post_id, '_featured', true) == '1';
    
    return $data;
}

/**
 * Custom breadcrumbs
 */
function autodebruin_breadcrumbs() {
    $separator = ' / ';
    $home_title = __('Home', 'autodebruin');
    
    // Start the breadcrumb with a link to your homepage
    echo '<nav class="breadcrumbs">';
    echo '<a href="' . esc_url(home_url('/')) . '">' . $home_title . '</a>' . $separator;
    
    if (is_single()) {
        // Single post
        if (get_post_type() === 'occasion') {
            echo '<a href="' . esc_url(get_post_type_archive_link('occasion')) . '">' . __('Occasions', 'autodebruin') . '</a>' . $separator;
        }
        echo '<span class="current">' . get_the_title() . '</span>';
    } elseif (is_page()) {
        // Page
        if ($post = get_post(get_the_ID())) {
            if ($post->post_parent) {
                $parent_id = $post->post_parent;
                $breadcrumbs = array();
                while ($parent_id) {
                    $page = get_page($parent_id);
                    $breadcrumbs[] = '<a href="' . esc_url(get_permalink($page->ID)) . '">' . get_the_title($page->ID) . '</a>';
                    $parent_id = $page->post_parent;
                }
                $breadcrumbs = array_reverse($breadcrumbs);
                foreach ($breadcrumbs as $crumb) {
                    echo $crumb . $separator;
                }
            }
        }
        echo '<span class="current">' . get_the_title() . '</span>';
    } elseif (is_archive()) {
        // Archive
        echo '<span class="current">' . get_the_archive_title() . '</span>';
    } elseif (is_search()) {
        // Search
        echo '<span class="current">' . __('Zoekresultaten voor: ', 'autodebruin') . get_search_query() . '</span>';
    } elseif (is_404()) {
        // 404
        echo '<span class="current">' . __('Pagina niet gevonden', 'autodebruin') . '</span>';
    }
    
    echo '</nav>';
}

/**
 * Get related occasions
 */
function autodebruin_get_related_occasions($post_id, $limit = 4) {
    $merk_terms = get_the_terms($post_id, 'merk');
    $merk_slug = $merk_terms && !is_wp_error($merk_terms) ? $merk_terms[0]->slug : '';
    
    $args = array(
        'post_type' => 'occasion',
        'posts_per_page' => $limit,
        'post__not_in' => array($post_id),
        'post_status' => 'publish'
    );
    
    if ($merk_slug) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'merk',
                'field' => 'slug',
                'terms' => $merk_slug,
            ),
        );
    }
    
    return new WP_Query($args);
}

/**
 * Check if occasion is featured
 */
function autodebruin_is_occasion_featured($post_id) {
    return get_post_meta($post_id, '_featured', true) == '1';
}
